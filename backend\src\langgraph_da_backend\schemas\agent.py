"""
This file contains the main agent class, i.e., the `langgraph.graph.StateGraph` implementation.
"""

import operator
from enum import Enum
from typing import Annotated, Any, Dict, List, TypedDict
from uuid import uuid4

import pandas as pd
import polars as pl
from langchain_core.messages import (
    AIMessage,
    AnyMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
)
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, MessagesState, StateGraph, add_messages
from pydantic import BaseModel, ConfigDict, Field

# --------------------------------------------------------------
# 1. State Definition
# --------------------------------------------------------------


class SupportedProtocols(str, Enum):
    """
    The supported protocols for data sources.
    """

    CSV = "csv"
    SQL = "sql"
    POSTGRES = "postgres"


class DataSource(BaseModel):
    protocol: SupportedProtocols = Field(
        description="The protocol of the data source."
    )
    uri: str = Field(description="The URI of the data source.")
    data: pl.LazyFrame | pl.DataFrame | pd.DataFrame = Field(
        description="The data object itself."
    )

    model_config = ConfigDict(arbitrary_types_allowed=True)


class ToolCall(BaseModel):
    tool_name: str = Field(description="The name of the tool to be called.")
    parameters: Dict[str, Any] = Field(
        description="The parameters for the tool."
    )


class AgentState(TypedDict):
    # * NOTE: Use Pydantic for State has certain limitations. see
    # * https://langchain-ai.github.io/langgraph/how-tos/graph-api/#use-pydantic-models-for-graph-state

    # -- Core conversational and control elements
    messages: Annotated[List[AnyMessage], add_messages]
    current_user_prompt: str
    terminate_signal: bool

    # -- Data pipeline states
    target_data_sources: List[str]
    loaded_data_objects: List[DataSource]

    # -- Planning states
    abstract_plan: str
    abstract_plan_feedback: str

    # -- Data Engineering & Tooling (These can run in parallel)
    current_data_object: pl.DataFrame | pd.DataFrame
    available_tools: List[str]

    # -- Reflection and Execution states
    reflection_hint: str
    # Using a Pydantic model here would enforce a clear structure for tool calls.
    tool_chains: List[ToolCall]
    tool_exec_results: List[Any]
    execution_reflection_feedback: str

    # -- Final output
    summary_report: Any


# --------------------------------------------------------------
# 2. Node Definitions (with more practical placeholder logic)
# --------------------------------------------------------------

# In a real app, you would have your LLM defined here
# from langchain_openai import ChatOpenAI
# llm = ChatOpenAI(model="gpt-4o")


def standby(state: AgentState) -> dict:
    """
    The initial node that decides the first step based on the input.
    This logic was previously in a conditional edge, but placing it in a node
    makes the graph more readable and aligned with the "Standby" state in the flowchart.
    """
    print("---STANDBY: CHECKING INPUT---")
    if state.get("terminate_signal"):
        print(">> Decision: Terminate signal received.")
        return {
            "messages": [
                AIMessage(content="Termination signal received. Shutting down.")
            ]
        }

    if state.get("data_source") and not state.get("loaded_data_objects"):
        print(">> Decision: Data source provided. Proceeding to data load.")
        return {
            "messages": [
                AIMessage(
                    content=f"Data source {state['data_source']} received. Loading data."
                )
            ]
        }

    if state.get("user_prompt") and state.get("loaded_data_objects"):
        print(
            ">> Decision: Prompt and data are present. Proceeding to abstractive planning."
        )
        return {
            "messages": [
                AIMessage(
                    content="Prompt and data found. Starting abstractive planning."
                )
            ]
        }

    print(">> Decision: Awaiting data or prompt.")
    return {
        "messages": [
            AIMessage(content="Please provide a data source or a prompt.")
        ]
    }


def data_load(state: AgentState) -> dict:
    """
    Loads data and allows for human-in-the-loop correction of the schema.
    """
    print("---LOADING DATA---")
    feedback = state.get("data_schema_feedback")
    if feedback:
        print(f"Applying human feedback to data schema: {feedback}")
        # Placeholder: apply feedback logic here

    # Placeholder: load data from state['data_source']
    loaded_data = {"data": [1, 2, 3], "schema": "initial_schema"}
    print(f"Loaded data with schema: {loaded_data['schema']}")
    # Clear feedback after applying it
    return {"loaded_data_objects": loaded_data, "data_schema_feedback": None}


def abstractive_planning(state: AgentState) -> dict:
    """
    Generates a high-level plan, allows for HIL correction.
    """
    print("---ABSTRACTIVE PLANNING---")
    feedback = state.get("abstract_plan_feedback")
    if feedback:
        print(f"Revising plan based on feedback: {feedback}")
        # In a real app, you would pass this feedback to the LLM.

    # Placeholder for LLM call to generate a plan
    plan = "1. Retrieve relevant analysis tools. 2. Perform data engineering. 3. Execute analysis."
    print(f"Generated Plan: {plan}")
    return {"abstract_plan": plan, "abstract_plan_feedback": None}


def human_in_the_loop(state: AgentState) -> dict:
    """
    A placeholder node where the graph will stop for human input.
    The 'interrupt_before' in the compile() step makes this work.
    """
    print("---AWAITING HUMAN FEEDBACK---")
    return {}


# These two nodes can now run in parallel
def tool_retrieval(state: AgentState) -> dict:
    """
    Retrieves relevant tools using a RAG system. Runs in parallel with data_engineering.
    """
    print("---(Parallel) RETRIEVING TOOLS---")
    tools = ["pandas_profiling", "scikit-learn_random_forest"]
    print(f"Retrieved Tools: {tools}")
    return {"available_tools": tools}


def data_engineering(state: AgentState) -> dict:
    """
    Performs data engineering tasks. Runs in parallel with tool_retrieval.
    """
    print("---(Parallel) DATA ENGINEERING---")
    engineered_data = {"cleaned_data": [1, 2, 3]}
    print(f"Engineered Data: {engineered_data}")
    return {"current_data_object": engineered_data}


def reflection(state: AgentState) -> dict:
    """
    This node acts as a "join" point after the parallel steps. It reflects on their combined output.
    """
    print("---REFLECTION ON DE AND TOOLS---")
    # In a real app, an LLM would check if the engineered data and retrieved tools are sufficient.
    tools = state.get("available_tools")
    data = state.get("current_data_object")

    if tools and data:
        hint = "good enough"
        print("Data and tools seem sufficient.")
    else:
        hint = "Incomplete data or tools. Please refine."
        print("Hint: Incomplete data or tools.")

    return {"reflection_hint": hint}


def execution_planning(state: AgentState) -> dict:
    print("---EXECUTION PLANNING---")
    # Placeholder: LLM generates a tool chain. Using the Pydantic model would look like this:
    # structured_llm = llm.with_structured_output(ToolCall)
    # tool_call = structured_llm.invoke("Plan to use pandas_profiling")
    tool_chains = [{"tool_name": "pandas_profiling", "parameters": {}}]
    print(f"Execution Plan: {tool_chains}")
    return {"tool_chains": tool_chains}


def execution_engine(state: AgentState) -> dict:
    print("---EXECUTION ENGINE---")
    results = ["Executed pandas_profiling successfully."]
    print(f"Execution Results: {results}")
    return {"tool_exec_results": results}


def execution_reflection(state: AgentState) -> dict:
    print("---EXECUTION REFLECTION---")
    feedback = "good enough"
    print(f"Execution Reflection Feedback: {feedback}")
    return {"execution_reflection_feedback": feedback}


def summary_report(state: AgentState) -> dict:
    print("---GENERATING SUMMARY REPORT---")
    report = "The data analysis is complete. The model shows promising results."
    print(f"Summary: {report}")
    return {"summary_report": report}


# --------------------------------------------------------------
# 3. Conditional Edge Logic
# --------------------------------------------------------------


def route_from_standby(state: AgentState) -> str:
    if state.get("terminate_signal"):
        return END
    if state.get("data_source") and not state.get("loaded_data_objects"):
        return "data_load"
    if state.get("user_prompt") and state.get("loaded_data_objects"):
        return "abstractive_planning"
    return "human_in_the_loop"  # Wait for more input


def route_after_load(state: AgentState) -> str:
    # After loading data, we wait for human verification/correction of the schema.
    # If a prompt is already available, we can proceed.
    if state.get("user_prompt"):
        return "abstractive_planning"
    return "human_in_the_loop"


def route_after_planning(state: AgentState) -> str:
    # After initial planning, we wait for human feedback.
    # If feedback is given, we re-plan. Otherwise, we proceed.
    if state.get("abstract_plan_feedback"):
        return "abstractive_planning"
    # This is the "fork" point for parallel execution.
    return ["data_engineering", "tool_retrieval"]


def route_after_reflection(state: AgentState) -> str:
    if state.get("reflection_hint") == "good enough":
        return "execution_planning"
    # Loop back to the parallel steps if reflection fails
    return ["data_engineering", "tool_retrieval"]


def route_after_execution(state: AgentState) -> str:
    if state.get("execution_reflection_feedback") == "good enough":
        return "summary_report"
    return "execution_planning"


# --------------------------------------------------------------
# 4. Graph Definition and Compilation
# --------------------------------------------------------------

# Using a MemorySaver is crucial for stateful graphs with interruptions.
memory = MemorySaver()
workflow = StateGraph(AgentState)

# Add nodes
workflow.add_node("standby", standby)
workflow.add_node("data_load", data_load)
workflow.add_node("human_in_the_loop", human_in_the_loop)
workflow.add_node("abstractive_planning", abstractive_planning)
# Parallel nodes
workflow.add_node("tool_retrieval", tool_retrieval)
workflow.add_node("data_engineering", data_engineering)
# Joiner and subsequent nodes
workflow.add_node("reflection", reflection)
workflow.add_node("execution_planning", execution_planning)
workflow.add_node("execution_engine", execution_engine)
workflow.add_node("execution_reflection", execution_reflection)
workflow.add_node("summary_report_node", summary_report)

# Define the flow
workflow.add_edge(START, "standby")
workflow.add_conditional_edges(
    "standby",
    route_from_standby,
    {
        "data_load": "data_load",
        "abstractive_planning": "abstractive_planning",
        "human_in_the_loop": "human_in_the_loop",
        "__end__": END,
    },
)
# The HIL node allows for corrections.
workflow.add_edge("data_load", "human_in_the_loop")
workflow.add_conditional_edges("human_in_the_loop", route_after_planning)

workflow.add_conditional_edges("abstractive_planning", route_after_planning)

# The "fork" to parallel execution. LangGraph waits for all branches to complete.
workflow.add_edge("tool_retrieval", "reflection")
workflow.add_edge("data_engineering", "reflection")

# The rest of the flow
workflow.add_conditional_edges("reflection", route_after_reflection)
workflow.add_edge("execution_planning", "execution_engine")
workflow.add_edge("execution_engine", "execution_reflection")
workflow.add_conditional_edges("execution_reflection", route_after_execution)
workflow.add_edge("summary_report_node", END)

# Compile the graph with the checkpointer and HIL interruption point.
app = workflow.compile(
    checkpointer=memory, interrupt_before=["human_in_the_loop"]
)


# --------------------------------------------------------------
# 5. Running the Graph (Example Invocation)
# --------------------------------------------------------------
if __name__ == "__main__":
    # Unique thread ID for the checkpointer
    thread_id = str(uuid4())
    config = {"configurable": {"thread_id": thread_id}}

    print("---STARTING: PROVIDING DATA SOURCE---")
    # Run 1: Provide data
    initial_state = {
        "messages": [HumanMessage(content="Load data from customers.csv")],
        "data_source": "customers.csv",
    }
    for event in app.stream(initial_state, config, stream_mode="values"):
        print(event)
        print("---")

    print("\n---GRAPH PAUSED FOR HIL---")
    print("Current state:", app.get_state(config).values)

    print("\n---RESUMING: PROVIDING PROMPT AND PLAN FEEDBACK---")
    # Run 2: Provide prompt and feedback, then resume
    app.update_state(
        config,
        {
            "messages": [HumanMessage(content="Analyze churn.")],
            "user_prompt": "Analyze customer churn.",
            "abstract_plan_feedback": "Focus on recent customers first.",  # Example feedback
        },
    )
    for event in app.stream(None, config, stream_mode="values"):
        print(event)
        print("---")

    print("\n---GRAPH EXECUTION FINISHED---")
    final_state = app.get_state(config).values
    print("Final Report:", final_state.get("summary_report"))
