import os
from functools import cached_property
from typing import List, Optional, Union

import numpy as np
import openai
from lancedb.embeddings import TextEmbeddingFunction
from lancedb.embeddings.registry import register
from loguru import logger


@register("general-embedding-function")
class CustomEmbeddingFunction(TextEmbeddingFunction):
    """A general-purpose embedding function that implements the LanceDB interface."""

    name: str = "text-embedding-ada-002"
    dim: Optional[int] = None
    base_url: Optional[str] = None
    default_headers: Optional[dict] = None
    organization: Optional[str] = None
    api_key: Optional[str] = None
    use_azure: bool = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ndims = None

    @staticmethod
    def sensitive_keys():
        return ["api_key"]

    def ndims(self) -> int:
        """Returns the dimension of the embeddings."""
        # TODO: add user config for ndims
        if self._ndims is None:
            # Compute dimension once and cache it
            self._ndims: int = len(self.generate_embeddings(["test"])[0])  # type: ignore
        return self._ndims

    @cached_property
    def _ndims(self):
        if self.name == "text-embedding-ada-002":
            return 1536
        elif self.name == "text-embedding-3-large":
            return self.dim or 3072
        elif self.name == "text-embedding-3-small":
            return self.dim or 1536
        else:
            return len(self.generate_embeddings(["test"])[0])

    def generate_embeddings(
        self, texts: Union[List[str], "np.ndarray"]
    ) -> List["np.array"]:
        """
        Get the embeddings for the given texts

        Parameters
        ----------
        texts: list[str] or np.ndarray (of str)
            The texts to embed
        """
        valid_texts = []
        valid_indices = []
        for idx, text in enumerate(texts):
            if text:
                valid_texts.append(text)
                valid_indices.append(idx)

        # TODO retry, rate limit, token limit
        try:
            kwargs = {
                "input": valid_texts,
                "model": self.name,
            }

            rs = self._openai_client.embeddings.create(**kwargs)
            valid_embeddings = {
                idx: v.embedding for v, idx in zip(rs.data, valid_indices)
            }
        except openai.BadRequestError:
            logger.exception("Bad request: %s", texts)
            return [None] * len(texts)
        except Exception:
            logger.exception("OpenAI embeddings error")
            raise
        return [valid_embeddings.get(idx, None) for idx in range(len(texts))]

    @cached_property
    def _openai_client(self):
        kwargs = {}

        if self.base_url:
            kwargs["base_url"] = self.base_url
        if self.default_headers:
            kwargs["default_headers"] = self.default_headers
        if self.organization:
            kwargs["organization"] = self.organization
        if self.api_key:
            kwargs["api_key"] = self.api_key
        if os.environ.get("OPENAI_API_KEY"):
            kwargs["api_key"] = os.environ.get("OPENAI_API_KEY")

        if self.use_azure:
            return openai.AzureOpenAI(**kwargs)
        else:
            return openai.OpenAI(**kwargs)

    def __deepcopy__(self, memo):
        # Prevent deepcopy issues with un-pickleable clients
        return self
