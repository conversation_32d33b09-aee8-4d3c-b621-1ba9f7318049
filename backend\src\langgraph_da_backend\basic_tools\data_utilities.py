from typing import Any, Dict, List, Union, Annotated, Callable
import json
from pathlib import Path

from ..utils.tool.tool_card import tool_card, register_tool

# Import the new, modular utility functions
_post_cast_datetime_cols: Callable
_post_cast_currency_cols: Callable
try:
    from ..utils.loader.post_cast import _post_cast_datetime_cols, _post_cast_currency_cols
    INTELLIGENT_LOADER_AVAILABLE = True
except ImportError:
    # Assign a no-op lambda to satisfy the type checker if import fails
    _post_cast_datetime_cols = lambda df, sample_size=100: df
    _post_cast_currency_cols = lambda df, sample_size=100: df
    INTELLIGENT_LOADER_AVAILABLE = False


@register_tool
@tool_card(
    keywords=["dataframe", "load", "csv", "parquet", "file", "data", "import"],
    description="Load data from CSV or Parquet files into a DataFrame with intelligent type detection"
)
def load_dataframe(
    file_path: Annotated[str, "Path to the CSV or Parquet file to load"],
    auto_detect_types: Annotated[bool, "Whether to automatically detect and convert data types"] = True
) -> Dict[str, Any]:
    """
    Loads data from a file into a DataFrame with intelligent type detection and returns comprehensive metadata.
    
    Features:
    - Automatic date/time detection and conversion
    - Currency format detection and conversion
    - Smart null value handling
    - Comprehensive metadata reporting
    
    Returns metadata about the loaded DataFrame including shape, columns, data types, and sample data.
    """
    try:
        import polars as pl # type: ignore
        
        if not Path(file_path).exists():
            return {"success": False, "error": f"File not found: {file_path}"}
        
        file_extension = Path(file_path).suffix.lower()
        
        # Basic polars loading first
        if file_extension == ".csv":
            lf = pl.scan_csv(file_path, null_values=["null", "None", "NULL", "nan", "NaN", ""])
            loader_used = "Basic Polars (CSV)"
        elif file_extension == ".parquet":
            lf = pl.scan_parquet(file_path)
            loader_used = "Basic Polars (Parquet)"
        else:
            return {"success": False, "error": f"Unsupported file type: {file_extension}"}

        # Apply intelligent type detection if requested and available
        if auto_detect_types and INTELLIGENT_LOADER_AVAILABLE:
            lf = _post_cast_datetime_cols(lf)
            lf = _post_cast_currency_cols(lf)
            loader_used += " + intelligent type casting"

        df = lf.collect()
        
        return {
            "success": True,
            "loader_used": loader_used,
            "shape": df.shape,
            "columns": df.columns,
            "dtypes": {name: str(dtype) for name, dtype in df.schema.items()},
            "sample_data": df.head(5).to_dicts(),
            "file_path": file_path,
            "file_type": file_extension,
            "memory_usage_mb": round(df.estimated_size() / (1024 * 1024), 2) if hasattr(df, 'estimated_size') else "N/A",
            "null_counts": {
                col: df[col].null_count() for col in df.columns
            }
        }
        
    except Exception as e:
        return {"success": False, "error": f"Error loading file: {str(e)}"}


@register_tool
@tool_card(
    keywords=["dataframe", "filter", "query", "subset", "condition", "where"],
    description="Filter DataFrame rows based on flexible conditions with multiple operators"
)
def filter_dataframe(
    df_data: Annotated[str, "File path or JSON string representation of DataFrame data"],
    column: Annotated[str, "Column name to filter on"],
    condition: Annotated[str, "Filter condition: 'equals', 'not_equals', 'greater_than', 'less_than', 'greater_equal', 'less_equal', 'contains', 'starts_with', 'ends_with', 'in_list'"],
    value: Annotated[Union[str, int, float, List], "Value to compare against (use list for 'in_list' condition)"],
    case_sensitive: Annotated[bool, "Whether string comparisons should be case sensitive"] = True
) -> Dict[str, Any]:
    """
    Filters DataFrame rows based on specified conditions with enhanced flexibility.
    
    Supports multiple comparison operators and string matching options.
    Returns the filtered data and comprehensive filtering statistics.
    """
    try:
        import polars as pl  # type: ignore
        
        # Handle input loading
        df = _load_dataframe_from_input(df_data)
        
        original_count = df.select(pl.len()).collect().item()
        
        # Build filter expression based on condition
        filter_expr = None
        
        if condition == 'equals':
            filter_expr = pl.col(column) == value
        elif condition == 'not_equals':
            filter_expr = pl.col(column) != value
        elif condition == 'greater_than':
            filter_expr = pl.col(column) > value
        elif condition == 'less_than':
            filter_expr = pl.col(column) < value
        elif condition == 'greater_equal':
            filter_expr = pl.col(column) >= value
        elif condition == 'less_equal':
            filter_expr = pl.col(column) <= value
        elif condition == 'contains':
            if case_sensitive:
                filter_expr = pl.col(column).str.contains(str(value))
            else:
                filter_expr = pl.col(column).str.to_lowercase().str.contains(str(value).lower())
        elif condition == 'starts_with':
            if case_sensitive:
                filter_expr = pl.col(column).str.starts_with(str(value))
            else:
                filter_expr = pl.col(column).str.to_lowercase().str.starts_with(str(value).lower())
        elif condition == 'ends_with':
            if case_sensitive:
                filter_expr = pl.col(column).str.ends_with(str(value))
            else:
                filter_expr = pl.col(column).str.to_lowercase().str.ends_with(str(value).lower())
        elif condition == 'in_list':
            if not isinstance(value, list):
                return {"error": "For 'in_list' condition, value must be a list"}
            filter_expr = pl.col(column).is_in(value)
        else:
            return {"error": f"Unsupported condition: {condition}"}
        
        # Apply filter
        filtered_df = df.filter(filter_expr).collect()
        filtered_count = filtered_df.shape[0]
        
        return {
            "filtered_data": filtered_df.to_dicts(),
            "filtering_stats": {
                "original_rows": original_count,
                "filtered_rows": filtered_count,
                "rows_removed": original_count - filtered_count,
                "retention_rate": round((filtered_count / original_count * 100), 2) if original_count > 0 else 0
            },
            "filter_applied": {
                "column": column,
                "condition": condition,
                "value": value,
                "case_sensitive": case_sensitive
            }
        }
        
    except Exception as e:
        return {"error": f"Error filtering DataFrame: {str(e)}"}


@register_tool
@tool_card(
    keywords=["dataframe", "sort", "order", "arrange", "rank"],
    description="Sort DataFrame by one or more columns with flexible ordering options"
)
def sort_dataframe(
    df_data: Annotated[str, "File path or JSON string representation of DataFrame data"],
    columns: Annotated[List[str], "List of column names to sort by (in order of priority)"],
    ascending: Annotated[Union[bool, List[bool]], "Whether to sort in ascending order. Can be single bool or list matching columns"] = True
) -> Dict[str, Any]:
    """
    Sorts DataFrame by specified columns with flexible ordering options.
    
    Supports multi-column sorting with individual ascending/descending control per column.
    Returns the sorted data with sorting metadata.
    """
    try:
        import polars as pl  # type: ignore
        
        df = _load_dataframe_from_input(df_data)
        
        # Validate columns exist
        available_columns = df.collect_schema().names()
        invalid_columns = [col for col in columns if col not in available_columns]
        if invalid_columns:
            return {"error": f"Columns not found: {invalid_columns}"}
        
        # Handle ascending parameter
        if isinstance(ascending, bool):
            descending = [not ascending] * len(columns)
        else:
            if len(ascending) != len(columns):
                return {"error": "Length of ascending list must match number of columns"}
            descending = [not asc for asc in ascending]
        
        # Sort the DataFrame
        sorted_df = df.sort(columns, descending=descending).collect()
        
        return {
            "sorted_data": sorted_df.to_dicts(),
            "shape": sorted_df.shape,
            "sort_configuration": {
                "columns": columns,
                "ascending_per_column": ascending if isinstance(ascending, list) else [ascending] * len(columns),
                "sort_order": "multi-column" if len(columns) > 1 else "single-column"
            }
        }
        
    except Exception as e:
        return {"error": f"Error sorting DataFrame: {str(e)}"}


@register_tool
@tool_card(
    keywords=["dataframe", "group", "aggregate", "summarize", "statistics"],
    description="Group DataFrame by columns and apply multiple aggregation functions"
)
def group_and_aggregate(
    df_data: Annotated[str, "File path or JSON string representation of DataFrame data"],
    group_by: Annotated[List[str], "List of columns to group by"],
    agg_configs: Annotated[List[Dict[str, str]], "List of aggregation configs: [{'column': 'col_name', 'function': 'sum'}, ...]"],
    include_counts: Annotated[bool, "Whether to include row counts for each group"] = True
) -> Dict[str, Any]:
    """
    Groups DataFrame by specified columns and applies multiple aggregation functions.
    
    Supports multiple aggregations per operation with flexible configuration.
    Returns the grouped and aggregated data with comprehensive metadata.
    """
    try:
        import polars as pl  # type: ignore
        
        df = _load_dataframe_from_input(df_data)
        
        # Validate group_by columns
        available_columns = df.collect_schema().names()
        invalid_group_cols = [col for col in group_by if col not in available_columns]
        if invalid_group_cols:
            return {"error": f"Group by columns not found: {invalid_group_cols}"}
        
        # Build aggregation expressions
        agg_exprs = []
        
        if include_counts:
            agg_exprs.append(pl.len().alias("group_count"))
        
        # Process aggregation configurations
        agg_funcs = {
            "sum": lambda col: pl.col(col).sum(),
            "mean": lambda col: pl.col(col).mean(),
            "median": lambda col: pl.col(col).median(),
            "min": lambda col: pl.col(col).min(),
            "max": lambda col: pl.col(col).max(),
            "count": lambda col: pl.col(col).count(),
            "std": lambda col: pl.col(col).std(),
            "var": lambda col: pl.col(col).var(),
            "first": lambda col: pl.col(col).first(),
            "last": lambda col: pl.col(col).last()
        }
        
        processed_configs = []
        for config in agg_configs:
            if not isinstance(config, dict) or 'column' not in config or 'function' not in config:
                return {"error": "Each agg_config must be a dict with 'column' and 'function' keys"}
            
            col_name = config['column']
            func_name = config['function']
            
            if col_name not in available_columns:
                return {"error": f"Aggregation column not found: {col_name}"}
            
            if func_name not in agg_funcs:
                return {"error": f"Unsupported aggregation function: {func_name}. Available: {list(agg_funcs.keys())}"}
            
            alias = f"{col_name}_{func_name}"
            agg_exprs.append(agg_funcs[func_name](col_name).alias(alias))
            processed_configs.append({"column": col_name, "function": func_name, "alias": alias})
        
        # Perform grouping and aggregation
        result = df.group_by(group_by).agg(agg_exprs).collect()
        
        return {
            "aggregated_data": result.to_dicts(),
            "shape": result.shape,
            "aggregation_summary": {
                "group_by_columns": group_by,
                "aggregations_applied": processed_configs,
                "total_groups": result.shape[0],
                "includes_counts": include_counts
            }
        }
        
    except Exception as e:
        return {"error": f"Error grouping and aggregating DataFrame: {str(e)}"}


@register_tool
@tool_card(
    keywords=["dataframe", "select", "columns", "subset", "projection"],
    description="Select specific columns from a DataFrame with optional renaming"
)
def select_columns(
    df_data: Annotated[str, "File path or JSON string representation of DataFrame data"],
    columns: Annotated[Union[List[str], Dict[str, str]], "List of column names to select, or dict for renaming {'old_name': 'new_name'}"]
) -> Dict[str, Any]:
    """
    Selects specific columns from a DataFrame with optional renaming capability.
    
    Supports both simple column selection and column renaming in a single operation.
    Returns the DataFrame with only the selected/renamed columns.
    """
    try:
        import polars as pl  # type: ignore
        
        df = _load_dataframe_from_input(df_data)
        available_columns = df.collect_schema().names()
        
        if isinstance(columns, list):
            # Simple column selection
            invalid_columns = [col for col in columns if col not in available_columns]
            if invalid_columns:
                return {"error": f"Columns not found: {invalid_columns}"}
            
            selected_df = df.select(columns).collect()
            operation_type = "selection"
            column_mapping = {col: col for col in columns}
            
        elif isinstance(columns, dict):
            # Column selection with renaming
            invalid_columns = [col for col in columns.keys() if col not in available_columns]
            if invalid_columns:
                return {"error": f"Columns not found: {invalid_columns}"}
            
            # Build select expressions with aliases
            select_exprs = [pl.col(old_name).alias(new_name) for old_name, new_name in columns.items()]
            selected_df = df.select(select_exprs).collect()
            operation_type = "selection_with_renaming"
            column_mapping = columns
            
        else:
            return {"error": "columns parameter must be either a list of column names or a dict for renaming"}
        
        return {
            "selected_data": selected_df.to_dicts(),
            "shape": selected_df.shape,
            "operation_summary": {
                "type": operation_type,
                "original_columns": len(available_columns),
                "selected_columns": len(selected_df.columns),
                "column_mapping": column_mapping
            },
            "dtypes": dict(selected_df.schema)
        }
        
    except Exception as e:
        return {"error": f"Error selecting columns: {str(e)}"}


@register_tool
@tool_card(
    keywords=["dataframe", "sample", "random", "subset", "statistics"],
    description="Get a random or systematic sample from a DataFrame"
)
def sample_dataframe(
    df_data: Annotated[str, "File path or JSON string representation of DataFrame data"],
    n_samples: Annotated[int, "Number of samples to take (use 0 for percentage-based sampling)"] = 10,
    sample_percentage: Annotated[float, "Percentage of data to sample (0-100, only used if n_samples=0)"] = 10.0,
    method: Annotated[str, "Sampling method: 'random' or 'systematic'"] = "random",
    random_seed: Annotated[int, "Random seed for reproducibility"] = 42,
    stratify_column: Annotated[str, "Column name for stratified sampling (optional)"] = ""
) -> Dict[str, Any]:
    """
    Takes a sample from a DataFrame using various sampling methods.
    
    Supports random sampling, systematic sampling, and stratified sampling.
    Returns the sampled data with comprehensive sampling metadata.
    """
    try:
        import polars as pl  # type: ignore
        
        df = _load_dataframe_from_input(df_data)
        total_rows = df.select(pl.len()).collect().item()
        
        if total_rows == 0:
            return {"error": "Cannot sample from empty DataFrame"}
        
        # Determine sample size
        if n_samples > 0:
            sample_size = min(n_samples, total_rows)
        else:
            sample_size = max(1, int(total_rows * sample_percentage / 100))
        
        sampled_df = None
        sampling_info = {"method": method, "seed": random_seed}
        
        if method == "random":
            if stratify_column and stratify_column in df.collect_schema().names():
                # Stratified sampling
                df_collected = df.collect()
                groups = df_collected.group_by(stratify_column, maintain_order=True)
                stratified_samples = []
                
                for group_df in groups:
                    group_size = len(group_df)
                    group_sample_size = max(1, int(sample_size * group_size / total_rows))
                    group_sample = group_df.sample(n=group_sample_size, seed=random_seed)
                    stratified_samples.append(group_sample)
                
                sampled_df = pl.concat(stratified_samples)
                sampling_info["stratify_column"] = stratify_column
                sampling_info["type"] = "stratified_random"
            else:
                # Simple random sampling
                sampled_df = df.sample(n=sample_size, seed=random_seed).collect()
                sampling_info["type"] = "simple_random"
                
        elif method == "systematic":
            # Systematic sampling
            interval = max(1, total_rows // sample_size)
            start_index = random_seed % interval if interval > 1 else 0
            
            indices = list(range(start_index, total_rows, interval))[:sample_size]
            sampled_df = df.collect().slice(0, total_rows).filter(
                pl.int_range(total_rows).is_in(indices)
            )
            sampling_info["type"] = "systematic"
            sampling_info["interval"] = interval
            sampling_info["start_index"] = start_index
        else:
            return {"error": f"Unsupported sampling method: {method}"}
        
        return {
            "sampled_data": sampled_df.to_dicts(),
            "sampling_stats": {
                "original_size": total_rows,
                "sample_size": sampled_df.shape[0],
                "sampling_ratio": round((sampled_df.shape[0] / total_rows * 100), 2),
                "sampling_info": sampling_info
            }
        }
        
    except Exception as e:
        return {"error": f"Error sampling DataFrame: {str(e)}"}


@register_tool
@tool_card(
    keywords=["dataframe", "head", "tail", "preview", "inspect"],
    description="Get the first, last, or random rows from a DataFrame for quick inspection"
)
def get_dataframe_preview(
    df_data: Annotated[str, "File path or JSON string representation of DataFrame data"],
    n_rows: Annotated[int, "Number of rows to retrieve"] = 5,
    position: Annotated[str, "Position to retrieve from: 'head', 'tail', or 'random'"] = "head",
    include_info: Annotated[bool, "Whether to include DataFrame info (shape, dtypes, etc.)"] = True
) -> Dict[str, Any]:
    """
    Gets a preview of DataFrame data from various positions with optional metadata.
    
    Supports head, tail, and random row sampling for quick data inspection.
    Returns the preview data along with comprehensive DataFrame information.
    """
    try:
        import polars as pl  # type: ignore
        
        df = _load_dataframe_from_input(df_data)
        total_rows = df.select(pl.len()).collect().item()
        
        if total_rows == 0:
            return {"error": "DataFrame is empty"}
        
        # Get preview data based on position
        if position == "head":
            preview_df = df.head(n_rows).collect()
        elif position == "tail":
            preview_df = df.tail(n_rows).collect()
        elif position == "random":
            preview_df = df.sample(n=min(n_rows, total_rows), seed=42).collect()
        else:
            return {"error": f"Invalid position: {position}. Use 'head', 'tail', or 'random'"}
        
        result = {
            "preview_data": preview_df.to_dicts(),
            "preview_info": {
                "position": position,
                "rows_shown": preview_df.shape[0],
                "total_rows": total_rows
            }
        }
        
        if include_info:
            result["dataframe_info"] = {
                "shape": (total_rows, len(df.collect_schema().names())),
                "columns": df.collect_schema().names(),
                "dtypes": {name: str(dtype) for name, dtype in df.collect_schema().items()},
                "memory_estimation": "Use load_dataframe tool for memory info"
            }
        
        return result
        
    except Exception as e:
        return {"error": f"Error getting DataFrame preview: {str(e)}"}


def _load_dataframe_from_input(df_data: str):
    """Helper function to load DataFrame from file path or JSON string."""
    import polars as pl  # type: ignore
    
    if df_data.endswith('.csv') or df_data.endswith('.parquet'):
        if INTELLIGENT_LOADER_AVAILABLE:
            # Replicate intelligent loading for consistency
            if df_data.endswith('.csv'):
                lf = pl.scan_csv(df_data)
            else: # .parquet
                lf = pl.scan_parquet(df_data)
            
            lf = _post_cast_datetime_cols(lf)
            lf = _post_cast_currency_cols(lf)
            return lf
        else:
            if df_data.endswith('.csv'):
                return pl.scan_csv(df_data)
            else:
                return pl.scan_parquet(df_data)
    else:
        # Assumes df_data is a JSON string of a list of dicts
        data = json.loads(df_data)
        return pl.LazyFrame(data) 