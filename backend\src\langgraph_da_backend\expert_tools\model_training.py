import json
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional

from langgraph_da_backend.utils.tool.tool_card import register_tool, tool_card

# It's assumed that pandas, scikit-learn, and joblib are installed.
# These imports will be resolved at runtime.

# --- Model Definitions ---
# Moved from predictor.py to be available for both training functions
REGRESSION_MODELS = [
    "linear_regression",
    "ridge",
    "random_forest_regressor",
    "decision_tree_regressor",
    "knn_regressor",
]

CLASSIFICATION_MODELS = [
    "logistic_regression",
    "svm",
    "naive_bayes",
    "random_forest_classifier",
    "decision_tree_classifier",
    "knn_classifier",
]


def _get_model_instance(model_name: str, hyperparameters: Dict[str, Any] = {}):
    """Helper to instantiate a model from its string name."""
    # Regression
    if model_name == "linear_regression":
        from sklearn.linear_model import LinearRegression  # type: ignore

        return LinearRegression(**hyperparameters)
    if model_name == "ridge":
        from sklearn.linear_model import Ridge  # type: ignore

        return Ridge(**hyperparameters)
    if model_name == "random_forest_regressor":
        from sklearn.ensemble import RandomForestRegressor  # type: ignore

        return RandomForestRegressor(**hyperparameters)
    if model_name == "decision_tree_regressor":
        from sklearn.tree import DecisionTreeRegressor  # type: ignore

        return DecisionTreeRegressor(**hyperparameters)
    if model_name == "knn_regressor":
        from sklearn.neighbors import KNeighborsRegressor  # type: ignore

        return KNeighborsRegressor(**hyperparameters)

    # Classification
    if model_name == "logistic_regression":
        from sklearn.linear_model import LogisticRegression  # type: ignore

        return LogisticRegression(**hyperparameters)
    if model_name == "svm":
        from sklearn.svm import SVC  # type: ignore

        # Ensure probability=True for AUC calculations later
        if "probability" not in hyperparameters:
            hyperparameters["probability"] = True
        return SVC(**hyperparameters)
    if model_name == "naive_bayes":
        from sklearn.naive_bayes import GaussianNB  # type: ignore

        return GaussianNB(**hyperparameters)
    if model_name == "random_forest_classifier":
        from sklearn.ensemble import RandomForestClassifier  # type: ignore

        return RandomForestClassifier(**hyperparameters)
    if model_name == "decision_tree_classifier":
        from sklearn.tree import DecisionTreeClassifier  # type: ignore

        return DecisionTreeClassifier(**hyperparameters)
    if model_name == "knn_classifier":
        from sklearn.neighbors import KNeighborsClassifier  # type: ignore

        return KNeighborsClassifier(**hyperparameters)

    raise ValueError(f"Unsupported model name: {model_name}")


@register_tool
@tool_card(
    keywords=[
        "train",
        "model",
        "regression",
        "machine learning",
        "fit",
        "predict",
    ],
)
def train_regression_model(
    X_train_path: Annotated[
        str,
        "Path to the processed training features file (X_train_processed.parquet).",
    ],
    y_train_path: Annotated[
        str, "Path to the training target variable file (y_train.parquet)."
    ],
    model_name: Annotated[
        str,
        f"The type of regression model to train. Available: {', '.join(REGRESSION_MODELS)}",
    ],
    output_dir: Annotated[str, "Directory to save the trained model artifact."],
    hyperparameters: Annotated[
        Optional[Dict[str, Any]], "Hyperparameters for the model."
    ] = None,
) -> Dict[str, Any]:
    """
    Trains a regression model.

    This function takes the processed training data, trains the specified model, and returns the path to the
    saved model, which can then be used for prediction and evaluation.
    """
    try:
        import joblib  # type: ignore
        import pandas as pd  # type: ignore

        if model_name not in REGRESSION_MODELS:
            return {
                "error": f"Invalid model_name. Choose from: {REGRESSION_MODELS}"
            }

        # --- Load Data ---
        X_train = pd.read_parquet(X_train_path)
        y_train = pd.read_parquet(y_train_path).iloc[
            :, 0
        ]  # Convert back to Series

        # --- Train Model ---
        model_params = hyperparameters or {}
        model = _get_model_instance(model_name, model_params)
        model.fit(X_train, y_train)

        # --- Save Model ---
        p_output = Path(output_dir)
        p_output.mkdir(parents=True, exist_ok=True)
        model_path = str(p_output / f"{model_name}.joblib")
        joblib.dump(model, model_path)

        return {
            "status": "success",
            "model_path": model_path,
            "summary": {
                "model_name": model_name,
                "task_type": "regression",
                "hyperparameters_used": model_params,
                "training_samples": len(X_train),
            },
        }
    except Exception as e:
        return {"error": f"Failed to train regression model: {str(e)}"}


@register_tool
@tool_card(
    keywords=["train", "model", "classification", "machine learning", "fit"],
)
def train_classification_model(
    X_train_path: Annotated[
        str,
        "Path to the processed training features file (X_train_processed.parquet).",
    ],
    y_train_path: Annotated[
        str, "Path to the training target variable file (y_train.parquet)."
    ],
    model_name: Annotated[
        str,
        f"The type of classification model to train. Available: {', '.join(CLASSIFICATION_MODELS)}",
    ],
    output_dir: Annotated[str, "Directory to save the trained model artifact."],
    hyperparameters: Annotated[
        Optional[Dict[str, Any]], "Hyperparameters for the model."
    ] = None,
) -> Dict[str, Any]:
    """
    Trains a classification model.

    This function takes the processed training data, trains the specified model, and returns the path to the
    saved model, which can then be used for prediction and evaluation.
    """
    try:
        import joblib  # type: ignore
        import pandas as pd

        if model_name not in CLASSIFICATION_MODELS:
            return {
                "error": f"Invalid model_name. Choose from: {CLASSIFICATION_MODELS}"
            }

        # --- Load Data ---
        X_train = pd.read_parquet(X_train_path)
        y_train = pd.read_parquet(y_train_path).iloc[
            :, 0
        ]  # Convert back to Series

        # --- Train Model ---
        model_params = hyperparameters or {}
        model = _get_model_instance(model_name, model_params)
        model.fit(X_train, y_train)

        # --- Save Model ---
        p_output = Path(output_dir)
        p_output.mkdir(parents=True, exist_ok=True)
        model_path = str(p_output / f"{model_name}.joblib")
        joblib.dump(model, model_path)

        return {
            "status": "success",
            "model_path": model_path,
            "summary": {
                "model_name": model_name,
                "task_type": "classification",
                "hyperparameters_used": model_params,
                "training_samples": len(X_train),
                "target_classes": y_train.unique().tolist(),
            },
        }
    except Exception as e:
        return {"error": f"Failed to train classification model: {str(e)}"}
