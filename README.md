# LangGraph DA

This is the repo for LangGraph based agentic data analysis appication. For now, we focus on the backend development.

The computation graph of LangGraph DA is shown below:

![workflow](resource/DAgraph.svg)

where the rounded rectangles represent the NODE of the LangGraph, the vanilla rectangles represent the GRAPH-level operations, and the dashed rectangles represent the invoked inputs.

A brief textual introduction of the whole process is as follows:

The process begins at the standard START node and immediately enters a Standby state.

1. Initiation

    The Standby state receives given data sources and user prompts, or even possible terminate signal. The workflow is triggered by any invoked input. A central decision point then checks the current invoked input.
    - data: Whenever a data source comes in, the system updates its global state `loaded_dataobjects` with the new data and returns to the Standby state. User should be able to interrupt and correct the data schema at this process.
    - prompt: When a user prompt comes in, the system always first updates the global state `messages` to record the user prompt. Then, based on if there is any loaded data, the system enters the planning stage (with data) or returns to the standby stage (without data).
    - terminate: When a terminate signal comes in, the system immediately terminates and returns to the END state. The workflow is terminated.

2. Abstractive Planning

    Once the user gives instructions on data analysis, the system performs Abstractive Planning to create a high-level strategy for addressing the user's request. At this stage, a user can interrupts to correct the strategy or add the methods they need. First, the agent refines the user's problem statement with standardized terminologies and concepts, eliminating any ambiguities or inconsistencies. Then, the proposed plan judges if current data is sufficient to address the user's problem; if not, in the following data engineering stage the agent shall try to perform transformations to make the data more suitable for analysis. The plan also give professional method outlines and keywords for later analysis tool retrieval. But this stage should not involve any coding or programming. The final abstractive plan is updated in the global state `abstract_plan`.

3. Iterative Tool Selection and Data Engineering

    After initial planning, the agent enters an iterative loop to prepare the data and select the right tools.

    - Tool Retrieval: The agent queries a RAG system (Retrieval-Augmented Generation) that contains information on professional data analysis (DA) tools. This step identifies potentially useful tools and updates the agent's state with `available_tools`.

    - Data Engineering: Concurrently, using predefined data engineering tools and a coding agent, this step processes the data. The output is an updated `current_dataobject`. This dataobject can either be some column transformations of the original data, or a new data object that is created by aggregating or other methods. 

    A Reflection step follows, where the agent assesses the result of the data engineering and tool retreival, by considering if they are sufficient to address the user's problem.
    If the result is "not good", the agent generates a hint to guide the next iteration and loops back to the Tool Retrieval and Data Engineering phase.
    If the result is "good enough", the workflow progresses to the next stage.

4. Execution Phase

    Once the data is properly engineered and tools are fully determined, the agent plans and executes the analysis.

    - Execution Planning: The agent creates a detailed, step-by-step plan, defining the sequence of tools to be used and their specific parameters based on the data schema (or even with data's statistical description). This updates the state with `tool_chains` and `tool_params`. 

    - Execution Engine: This component runs the planned tool chains. The results are captured in the global state as `tool_exec_results`. If errors occur or the results are not sufficient, the workflow shall loop back to the Execution Planning stage via reflection for refinement.

5. Result Presentation

    Finally, the agent presents the results in a user-friendly report, with clear visualizations and explanations. The report is updated in the global state `summary_report`. 

## Development Guideline

We will still use `conda` + `uv` for environment management.
Please note: **DO NOT** let the AI coder touch the dependency files (`pyprojec.toml`, `requirements.txt`, etc). Use `uv add` to add your dependency modules.