from typing import Annotated, Any, Dict, List, Literal, Optional, Tuple, Union

import pandas as pd
import polars as pl
from langchain_core.tools import tool
from langgraph_da_backend.basic_tools.tool_schema import (
    MissValuesStrategy,
    NormStrategy,
    OutliersStrategy,
)
from typing_extensions import TypedDict


class MissingDataHandleStrategy(TypedDict):
    col_name: str
    strategy: MissValuesStrategy


class OutlierDataHandleStrategy(TypedDict):
    col_name: str
    strategy: OutliersStrategy


class NormalizationStrategy(TypedDict):
    col_name: str
    strategy: NormStrategy


def handle_missing_values(
    df: Union[pd.DataFrame, pl.DataFrame, pl.LazyFrame],
    strategies: Annotated[
        MissingDataHandleStrategy,
        "A dict containing k-v pairs as <column_name>: <strategy>, specifies the strategy to handle missing values for each column. Available strategies: 'drop_col', 'drop_rows', 'mean', 'median', 'mode', 'forward', 'backward', 'constant'.",
    ],
    fill_values: Annotated[
        Optional[Dict[str, Any]],
        "A dict containing k-v pairs as <column_name>: <value>, specifies the constant values for imputation when strategy is 'constant'.",
    ] = None,
) -> Dict[str, Any]:
    """
    Applies specified strategies to handle missing values for columns.
    Available strategies:
        - 'drop_col': Drop the entire column if it has missing values. Useful when the column is not important for analysis, or it has over 50% missing values.
        - 'drop_rows': Drop the rows with missing values in the column. Useful when the missing values have a significant impact on the analysis and cannot / should not be easily imputed, like fitting a model.
        - 'mean': Impute missing values with the mean of the column. Only works for numeric columns.
        - 'median': Impute missing values with the median of the column. Only works for numeric columns.
        - 'mode': Impute missing values with the mode of the column.
        - 'forward': Impute missing values with the value of the previous non-missing value.
        - 'backward': Impute missing values with the value of the next non-missing value.
        - 'constant': Fill missing values with a constant value specified in the `fill_values` parameter.
    When the user prompt does not specify a strategy for a column. Decide based on the following steps:
        - If the column has more than 50% missing values, drop the entire column.
        - If a column only has few missing values, and it is important for fitting models or analysis, simply drop the rows with missing values.
        - For non-numeric columns, use a special constant value like 'unknown' or 'not_available' to impute missing values.

    NOTE: this is an **in-place** operation on the input DataFrame.
    """
    try:
        if isinstance(df, pl.DataFrame):
            pass
        elif isinstance(df, pd.DataFrame):
            df = pl.from_pandas(df)
        elif isinstance(df, pl.LazyFrame):
            df = df.collect()

        n_rows_initial, n_cols_initial = df.shape
        report: Dict[str, Any] = {"actions": [], "columns_dropped": []}

        const_fill_values = fill_values or {}

        cols_to_drop = [
            col for col, strat in strategies.items() if strat == "drop_col"
        ]
        if cols_to_drop:
            df = df.drop(cols_to_drop)
            report["columns_dropped"] = cols_to_drop
            report["actions"].extend(
                [
                    f"Column '{col}': Dropped based on 'drop_col' strategy."
                    for col in cols_to_drop
                ]
            )

        for col, strat in strategies.items():
            if col in cols_to_drop or col not in df.columns:
                continue

            if df[col].null_count() == 0:
                continue

            action_taken = ""
            if strat == "drop_rows":
                original_rows = df.height
                df = df.filter(pl.col(col).is_not_null())
                rows_removed_col = original_rows - df.height
                if rows_removed_col > 0:
                    action_taken = f"Column '{col}': Dropped {rows_removed_col} rows with missing values."
            elif strat in ("mean", "median") and df[col].dtype.is_numeric():
                fill_val = (
                    df[col].mean() if strat == "mean" else df[col].median()
                )
                df = df.with_columns(pl.col(col).fill_null(fill_val))
                action_taken = f"Column '{col}': Imputed missing values with {strat} ({fill_val!r})."
            elif strat == "mode":
                fill_val = df[col].mode().item()
                if fill_val is not None:
                    df = df.with_columns(pl.col(col).fill_null(fill_val))
                    action_taken = f"Column '{col}': Imputed missing values with mode ('{fill_val!r}')."
            elif strat in ("forward", "backward"):
                df = df.with_columns(pl.col(col).fill_null(strategy=strat))  # type: ignore
                action_taken = f"Column '{col}': Applied {strat} imputation."
            elif strat == "constant" and col in const_fill_values:
                fill_val = const_fill_values[col]
                df = df.with_columns(pl.col(col).fill_null(fill_val))
                action_taken = f"Column '{col}': Filled missing values with constant '{fill_val!r}'."

            if action_taken:
                report["actions"].append(action_taken)

        report["initial_shape"] = (n_rows_initial, n_cols_initial)
        report["final_shape"] = df.shape
        rows_removed_total = n_rows_initial - len(df)

        return {
            "status": "success",
            "summary": report,
            "rows_removed": rows_removed_total,
            "df": df,
        }
    except Exception as e:
        return {"error": f"Failed during missing value handling: {str(e)}"}


def handle_outliers(
    df: Union[pd.DataFrame, pl.DataFrame, pl.LazyFrame],
    strategies: Annotated[
        OutlierDataHandleStrategy,
        "A dict containing k-v pairs as <column_name>: <strategy>, specifies the strategy to handle outliers for each column. Available strategies: 'remove', 'winsorize'.",
    ],
) -> Dict[str, Any]:
    """
    Applies a cleaning method (remove, winsorize) to outliers in **numeric** columns.
    Available strategies:
        - 'remove': Remove the rows with outliers in the column.
        - 'winsorize': Winsorize the values in the column to the 5%-95% quantile range.
    """
    try:
        if isinstance(df, pl.DataFrame):
            pass
        elif isinstance(df, pd.DataFrame):
            df = pl.from_pandas(df)
        elif isinstance(df, pl.LazyFrame):
            df = df.collect()

        report: Dict[str, Any] = {"actions": [], "initial_shape": df.shape}
        n_rows_initial = len(df)

        for col, strat in strategies.items():
            if df[col].dtype.is_numeric():
                # compute the iqr first
                q1 = df[col].quantile(0.25)
                q3 = df[col].quantile(0.75)
                iqr = q3 - q1  # type: ignore
                lower_bound = q1 - 1.5 * iqr  # type: ignore
                upper_bound = q3 + 1.5 * iqr  # type: ignore
                if strat == "remove":
                    df = df.filter(
                        (pl.col(col) >= lower_bound)
                        & (pl.col(col) <= upper_bound)
                    )
                    rows_removed_col = n_rows_initial - len(df)
                    report["actions"].append(
                        f"Column '{col}': Removed {rows_removed_col} rows with outliers."
                    )
                elif strat == "winsorize":
                    df = df.with_columns(
                        [pl.col(col).clip(lower_bound, upper_bound)]
                    )
                    report["actions"].append(
                        f"Column '{col}': Winsorized values to the 5%-95% quantile range."
                    )

        report["final_shape"] = df.shape
        rows_removed_total = n_rows_initial - len(df)

        return {
            "status": "success",
            "summary": report,
            "rows_removed": rows_removed_total,
            "df": df,
        }
    except Exception as e:
        return {"error": f"Failed during outlier handling: {str(e)}"}


def handle_duplicates(
    df: Union[pd.DataFrame, pl.DataFrame, pl.LazyFrame],
    col_names: Annotated[
        Union[List[str], Tuple[str]],
        "A list of column names to check for duplicates.",
    ],
) -> Dict[str, Any]:
    """
    Perform deduplication based on the specified columns.
    For now, only exact duplicates are considered.
    """
    try:
        if isinstance(df, pl.DataFrame):
            pass
        elif isinstance(df, pd.DataFrame):
            df = pl.from_pandas(df)
        elif isinstance(df, pl.LazyFrame):
            df = df.collect()

        report: Dict[str, Any] = {"actions": [], "initial_shape": df.shape}
        n_rows_initial = len(df)

        df = df.unique(subset=col_names, maintain_order=True)
        rows_removed = n_rows_initial - len(df)
        report["actions"].append(f"Removed {rows_removed} duplicate rows.")
        report["final_shape"] = df.shape

        return {
            "status": "success",
            "summary": report,
            "rows_removed": rows_removed,
            "df": df,
        }
    except Exception as e:
        return {"error": f"Failed during duplicate handling: {str(e)}"}


def perform_sorting(
    df: Union[pd.DataFrame, pl.DataFrame, pl.LazyFrame],
    col_names: Annotated[
        Union[List[str], Tuple[str]],
        "A list of column names to sort by.",
    ],
    descending: Annotated[
        Union[List[bool], Tuple[bool]],
        "A list of booleans to specify the sort order for each column.",
    ],
) -> Dict[str, Any]:
    """
    Sort the DataFrame based on the specified columns and order.
    """
    try:
        if isinstance(df, pl.DataFrame):
            pass
        elif isinstance(df, pd.DataFrame):
            df = pl.from_pandas(df)
        elif isinstance(df, pl.LazyFrame):
            df = df.collect()

        df = df.sort(col_names, descending=descending, maintain_order=True)

        return {
            "status": "success",
            "df": df,
        }

    except Exception as e:
        return {"error": f"Failed during sorting: {str(e)}"}


def perform_normalization(
    df: Union[pd.DataFrame, pl.DataFrame, pl.LazyFrame],
    strategies: Annotated[
        NormalizationStrategy,
        "A dict containing k-v pairs as <column_name>: <strategy>, specifies the strategy to normalize each column. Available strategies:'min_max', 'z_score'.",
    ],
) -> Dict[str, Any]:
    """
    Normalize the DataFrame based on the specified columns and strategy.
    The specified columns must be numeric.
    """
    try:
        if isinstance(df, pl.DataFrame):
            pass
        elif isinstance(df, pd.DataFrame):
            df = pl.from_pandas(df)
        elif isinstance(df, pl.LazyFrame):
            df = df.collect()

        for col, strat in strategies.items():
            if strat == "min_max":
                min_val = df[col].min()
                max_val = df[col].max()
                df = df.with_columns(
                    (pl.col(col) - min_val) / (max_val - min_val)  # type: ignore
                )
            elif strat == "z_score":
                mean = df[col].mean()
                std = df[col].std()
                df = df.with_columns((pl.col(col) - mean) / std)

        return {
            "status": "success",
            "df": df,
        }

    except Exception as e:
        return {"error": f"Failed during normalization: {str(e)}"}


# TODO@panyu: I will wrap up a time-series preprocessing function here. The tool does the following: check the values exist (at least); check whether the timestamp cols exist (by optional parameter) and convert to standard pandas datetime type; check if any column seems to be id column; convert the timestamp col to evenly sampled ones (using https://github.com/stringertheory/traces) - I will think more about resampling strategies; and sort the data in ascending order of timestamp.
