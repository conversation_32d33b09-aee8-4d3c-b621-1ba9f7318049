from datetime import <PERSON><PERSON>ta
from typing import Any, Dict, List, Literal, Optional, Tuple, Union

import numpy as np
import pandas as pd
import polars as pl
from loguru import logger
from scipy.stats import chi2_contingency  # type: ignore

AGGREGATION_FUNCTIONS = {
    "sum": lambda col: pl.col(col).sum(),
    "mean": lambda col: pl.col(col).mean(),
    "median": lambda col: pl.col(col).median(),
    "min": lambda col: pl.col(col).min(),
    "max": lambda col: pl.col(col).max(),
    "count": lambda col: pl.col(col).count(),
    "std": lambda col: pl.col(col).std(),
    "var": lambda col: pl.col(col).var(),
    "first": lambda col: pl.first(col),
    "last": lambda col: pl.last(col),
}


def detect_column_types(
    obj: Union[pl.DataFrame, pl.LazyFrame, pd.DataFrame],
    categorical_threshold: float = 0.2,
) -> Dict[str, List[str]]:
    """
    Detects the data types of each column in a DataFrame.
    The final dict always contains 4 keys:
        - numeric
        - text
        - categorical
        - datetime
    """
    if isinstance(obj, pl.DataFrame):
        df = obj
    elif isinstance(obj, pl.LazyFrame):
        df = obj.collect()
    elif isinstance(obj, pd.DataFrame):
        df = pl.from_pandas(obj)
    else:
        raise TypeError(f"Unsupported type: {type(obj)}")

    df_len = df.height

    df_unique_dict = {
        key: value[0]
        for key, value in df.select(
            [pl.col(col).n_unique().alias(col) for col in df.schema.names()]
        )
        .to_dict()
        .items()
    }

    if df_len > 0:
        unique_ratio = {
            key: value / df_len for key, value in df_unique_dict.items()
        }
    else:
        unique_ratio = {key: 1 for key in df_unique_dict.keys()}

    categorical_cols = [
        name
        for name, dtype in df.schema.items()
        if dtype in (pl.Categorical, pl.Boolean, pl.Enum)
    ]
    categorical_cols += [
        name
        for name, ratio in unique_ratio.items()
        if (ratio <= categorical_threshold or df_unique_dict[name] <= 3)
        and not (
            df.schema[name].is_float()
            or df.schema[name].is_decimal()
            or df.schema[name].is_temporal()
        )
    ]
    categorical_cols = list(set(categorical_cols))

    numeric_cols = [
        name
        for name, dtype in df.schema.items()
        if dtype.is_numeric() and name not in categorical_cols
    ]
    text_cols = [
        name
        for name, dtype in df.schema.items()
        if dtype in (pl.Utf8, pl.String) and name not in categorical_cols
    ]
    datetime_cols = [
        name
        for name, dtype in df.schema.items()
        if dtype in (pl.Date, pl.Datetime, pl.Time)
        and name not in categorical_cols
    ]

    # check if all columns are categorized
    if len(categorical_cols) + len(numeric_cols) + len(text_cols) + len(
        datetime_cols
    ) != len(df.schema.names()):
        logger.warning(
            f"Some columns are not categorized, please check the data types of {set(categorical_cols + numeric_cols + text_cols + datetime_cols) - set(df.schema.names())}"
        )

    return {
        "numeric": numeric_cols,
        "text": text_cols,
        "categorical": categorical_cols,
        "datetime": datetime_cols,
        "undefined": [
            name
            for name in df.schema.names()
            if name
            not in (categorical_cols + numeric_cols + text_cols + datetime_cols)
        ],
    }


def describe(df: pl.LazyFrame, do_corr_analysis: bool = True):
    # For any column, we should always compute the missing and duplicate / unique values. Then based on unique values, we decide whether it is categorical or not. And then we can apply col-specific analysis.

    # DATATYPE: numeric, textual / string, categorical, datetime
    try:
        df_len = int(df.select(pl.len()).collect().item())
        df_dup_len = int(_get_num_duplicate_rows(df))
        df_missing_dict = _get_num_missing_values(df)
        df_outlier_dict, _ = _get_num_outliers(df)
        df_unique_dict = _get_num_unique_cells_by_col(df)

        coltype_rslt = detect_column_types(df)
        categorical_cols = coltype_rslt["categorical"]
        numeric_cols = coltype_rslt["numeric"]
        text_cols = coltype_rslt["text"]
        datetime_cols = coltype_rslt["datetime"]

        rslt_table = {}
        for col in df.collect_schema().names():
            if col in categorical_cols:
                rslt_table[col] = _describe_categorical_col(df, col)
            elif col in numeric_cols:
                rslt_table[col] = _describe_numeric_col(df, col)
            elif col in text_cols:
                rslt_table[col] = _describe_text_col(df, col)
            elif col in datetime_cols:
                rslt_table[col] = _describe_datetime_col(df, col)

        rslt_dict = {
            "num_rows": df_len,
            "num_duplicate_rows": df_dup_len,
            "num_missing_values": df_missing_dict,
            "num_outliers": df_outlier_dict,
            "num_unique_cells": df_unique_dict,
            "col_analysis": rslt_table,
            "column_types": df.collect_schema(),
        }

        if do_corr_analysis:
            corr_report = _perform_correlation_analysis(
                df, categorical_cols, numeric_cols
            )
            rslt_dict["correlation_analysis"] = corr_report

        # TODO@panyu: I will have to think how to manage such a complex report.
        return rslt_dict

    except Exception as e:
        raise ValueError(f"Error performing analysis: {str(e)}")


def _get_num_missing_values(df: pl.LazyFrame) -> Dict[str, int]:
    """
    Get the number of missing values in each column of a DataFrame.
    """
    return {
        key: value[0]
        for key, value in df.select(
            [
                pl.col(col).is_null().sum().alias(col)
                for col in df.collect_schema().names()
            ]
        )
        .collect()
        .to_dict()
        .items()
    }


def _get_num_duplicate_rows(df: pl.LazyFrame) -> int:
    """
    Get the number of duplicate rows in a DataFrame.
    """
    return (
        df.select(pl.len()).collect().item()
        - df.unique().select(pl.len()).collect().item()
    )


def _get_num_unique_cells_by_col(df: pl.LazyFrame) -> Dict[str, int]:
    """
    Get the number of unique cells in each column of a DataFrame.
    """
    return {
        key: value[0]
        for key, value in df.select(
            [
                pl.col(col).n_unique().alias(col)
                for col in df.collect_schema().names()
            ]
        )
        .collect()
        .to_dict()
        .items()
    }


def _get_num_outliers(
    df: pl.LazyFrame,
    method: Literal["iqr", "zscore"] = "iqr",
    threshold: float = 1.5,
) -> Tuple[Dict[str, int], Dict[str, Dict[str, float]]]:
    """
    Get the number of outliers in each column of a DataFrame.
    """
    numerical_cols = [
        name
        for name, dtype in df.collect_schema().items()
        if dtype.is_numeric()
    ]
    if not numerical_cols:
        return {}, {}

    outlier_count: Dict[str, int] = {}
    outlier_stats: Dict[str, Dict[str, float]] = {}

    try:
        if method == "iqr":
            # compute q1 and q3 then iqr
            quantiles = df.select(
                [
                    pl.col(col).quantile(0.25).alias(f"{col}_q1")
                    for col in numerical_cols
                ]
                + [
                    pl.col(col).quantile(0.75).alias(f"{col}_q3")
                    for col in numerical_cols
                ]
            ).collect()

            for col in numerical_cols:
                q1 = quantiles[f"{col}_q1"][0]
                q3 = quantiles[f"{col}_q3"][0]
                iqr = q3 - q1
                lb = q1 - threshold * iqr
                ub = q3 + threshold * iqr
                outlier_count[col] = (
                    df.filter(pl.col(col) < lb)
                    .filter(pl.col(col) > ub)
                    .select(pl.len())
                    .collect()
                    .item()
                )
                outlier_stats[col] = {
                    "lower_bound": lb,
                    "upper_bound": ub,
                }

        elif method == "zscore":
            dists = df.select(
                [
                    pl.col(col).mean().alias(f"{col}_mean")
                    for col in numerical_cols
                ]
                + [
                    pl.col(col).std().alias(f"{col}_std")
                    for col in numerical_cols
                ]
            ).collect()

            for col in numerical_cols:
                mean = dists[f"{col}_mean"][0]
                std = dists[f"{col}_std"][0]
                outlier_count[col] = (
                    df.filter((pl.col(col) - mean).abs() > threshold * std)
                    .select(pl.len())
                    .collect()
                    .item()
                )
                outlier_stats[col] = {
                    "mean": mean,
                    "std": std,
                }

        return outlier_count, outlier_stats
    except Exception as e:
        raise ValueError(
            f"Error computing outliers using {method} with threshold {threshold}: {str(e)}"
        )


def _describe_numeric_col(
    df: pl.LazyFrame, col: str
) -> Dict[str, Union[int, float, str]]:
    """
    Get descriptive statistics for a numeric column in a DataFrame.

    Including:
        - min
        - max
        - range
        - 5-th percentile
        - 25-th percentile
        - 50-th percentile (median)
        - 75-th percentile
        - 95-th percentile
        - interquartile range (IQR)
        - mean
        - standard deviation
        - coefficient of variation (CV)
        - skewness
        - kurtosis
    """
    min_value = df.select(pl.min(col)).collect().item()
    max_value = df.select(pl.max(col)).collect().item()
    range_value = max_value - min_value
    quartile_1 = df.select(pl.quantile(col, 0.25)).collect().item()
    quartile_3 = df.select(pl.quantile(col, 0.75)).collect().item()
    iqr_value = quartile_3 - quartile_1
    median_value = df.select(pl.median(col)).collect().item()
    mean_value = df.select(pl.mean(col)).collect().item()
    std_value = df.select(pl.std(col)).collect().item()
    cv_value = std_value / mean_value if mean_value != 0 else 0
    skew_value = df.select(col).collect().to_series().skew()
    kurt_value = df.select(col).collect().to_series().kurtosis()
    return {
        "min": min_value,
        "max": max_value,
        "range": range_value,
        "5-th percentile": df.select(pl.quantile(col, 0.05)).collect().item(),
        "25-th percentile": quartile_1,
        "50-th percentile": median_value,
        "75-th percentile": quartile_3,
        "95-th percentile": df.select(pl.quantile(col, 0.95)).collect().item(),
        "interquartile range": iqr_value,
        "mean": mean_value,
        "standard deviation": std_value,
        "coefficient of variation": cv_value,
        "skewness": skew_value if isinstance(skew_value, float) else "nan",
        "kurtosis": kurt_value if isinstance(kurt_value, float) else "nan",
    }


def _describe_text_col(df: pl.LazyFrame, col: str):
    """
    Get descriptive statistics for a text column in a DataFrame.

    NOTE: I did not think up very much descriptive statistics for text columns. Maybe later we can add more. And for now, as we compute the unique for the whole dataframe, we do not need to compute it for each column.
    """
    lengths = (
        df.select(
            pl.col(col)
            .filter(pl.col(col).is_not_null())
            .str.len_chars()
            .alias("lengths")
        )
        .collect()["lengths"]
        .drop_nulls()
    )
    min_len, max_len, mean_len, median_len = (
        lengths.min(),
        lengths.max(),
        lengths.mean(),
        lengths.median(),
    )

    word_regex = r"\w+"
    word_counts = (
        df.select(col)  # Select only the relevant column early
        .filter(pl.col(col).is_not_null())  # Ignore null values
        .select(  # Apply transformations
            pl.col(col)
            .str.to_lowercase()  # Case-insensitive counting
            .str.extract_all(word_regex)  # Extract all word sequences
            .alias("words")
        )
        .explode("words")  # Put each word on its own row
        .rename({"words": "word"})  # Rename for clarity
        .filter(
            pl.col("word").str.len_chars() > 0
        )  # Ensure word is not empty (shouldn't happen with \w+)
        .group_by("word")  # Group by the extracted word
        .agg(pl.count().alias("count"))  # Count occurrences of each word
        .collect()
        # We need the total count *after* grouping to calculate percentage
    )
    total_word_count = word_counts["count"].sum()
    word_count_rslt = (
        word_counts.with_columns(
            ((pl.col("count") / total_word_count) * 100).alias("percentage")
        )
        .sort("count", descending=True)
        .limit(10)
        .to_dict()
    )

    rslt = {
        "char_min_length": min_len,
        "char_max_length": max_len,
        "char_mean_length": mean_len,
        "char_median_length": median_len,
        "word_count_top_10": word_count_rslt["word"].to_list(),
        "word_count_top_10_percentage": word_count_rslt["percentage"].to_list(),
    }

    return rslt


def _describe_categorical_col(df: pl.LazyFrame, col: str):
    """
    Get descriptive statistics for a categorical column in a DataFrame.
    """
    top_10_values = (
        df.group_by(col)
        .agg(pl.len().alias("_count"))
        .sort("_count", descending=True)
        .head(10)
        .collect()
        .to_dict()
    )
    top_10_dict = {
        k: v for k, v in zip(top_10_values[col], top_10_values["_count"])
    }
    return {
        "top_10_appearances": top_10_dict,
    }


def _describe_datetime_col(df: pl.LazyFrame, col: str):
    """
    Get descriptive statistics for a datetime column in a DataFrame.

    NOTE: I did not handle the timezone issue. Maybe later we add some related codes for more robust analysis.
    In the future we also need a more strict time series analysis.
    """
    earliest_time = df.select(pl.min(col)).collect().item()
    latest_time = df.select(pl.max(col)).collect().item()
    time_span = str((latest_time - earliest_time))
    sorted_times = (
        df.filter(pl.col(col).is_not_null())
        .select(col)
        .sort(col, descending=False)
        .collect()
    )
    pd_sorted = sorted_times.to_pandas()
    pd_sorted_seconds = pd_sorted.astype("int64") / 1e3
    intervals = pd_sorted_seconds.diff().dropna()

    mean_interval = str(timedelta(seconds=intervals.mean().item()))
    std_interval = str(timedelta(seconds=intervals.std().item()))
    median_interval = str(timedelta(seconds=intervals.median().item()))

    try:
        inferred_freq = pd.infer_freq(pd_sorted)  # type: ignore
    except ValueError:
        inferred_freq = "unknown"

    return {
        "earliest_time": earliest_time.strftime("%Y-%m-%d %H:%M:%S"),
        "latest_time": latest_time.strftime("%Y-%m-%d %H:%M:%S"),
        "time_span": time_span,
        "mean_interval": mean_interval,
        "std_interval": std_interval,
        "median_interval": median_interval,
        "inferred_freq": inferred_freq,
    }


def _perform_correlation_analysis(
    df: pl.LazyFrame,
    categorical_cols: List[str],
    numeric_cols: List[str],
):
    """
    Perform correlation analysis on a DataFrame.

    Args:
        df: Input LazyFrame
        categorical_cols: List of categorical columns
        numeric_cols: List of numeric columns

    Returns:
        Dictionary with correlation results
    """
    results: Dict[tuple, Dict[str, Optional[Union[float, str]]]] = {}
    logger.debug("Starting correlation analysis...")

    # 1. numeric - numeric (Pearson's correlation)
    for i, col1 in enumerate(numeric_cols):
        for col2 in numeric_cols[i + 1 :]:
            try:
                sub = df.select([col1, col2]).drop_nulls().collect()
                if sub.shape[0] < 2:
                    continue
                coef = sub.select(pl.corr(col1, col2)).item()
                if coef is not None and not np.isnan(coef):
                    results[(col1, col2)] = {
                        "correlation_pearson_coef": round(float(coef), 4)
                    }
            except Exception as e:
                logger.warning(
                    f"Could not compute Pearson correlation between {col1} and {col2}: {str(e)}"
                )
                continue

    # 2. numeric - categorical (Point-Biserial correlation for binary categorical, Eta squared for multi-category)
    for catcol in categorical_cols:
        for numcol in numeric_cols:
            try:
                sub = df.select([catcol, numcol]).drop_nulls().collect()
                if sub.shape[0] < 2:
                    continue

                cat_np_array = sub[catcol].to_numpy()
                num_np_array = sub[numcol].to_numpy()

                # Convert to float arrays
                num_np_array = num_np_array.astype(float)

                levels = np.unique(cat_np_array)
                if len(levels) <= 1:
                    continue
                elif len(levels) == 2:
                    # Point-Biserial correlation for binary categorical variables
                    binary = (cat_np_array == levels[0]).astype(float)
                    coef = np.corrcoef(binary, num_np_array)[0, 1]
                    if np.isfinite(coef):
                        results[(catcol, numcol)] = {
                            "correlation_point_biserial_coef": round(
                                float(coef), 4
                            )
                        }
                else:
                    # Eta squared for multi-category variables
                    grand_mean = np.mean(num_np_array)
                    ss_total = np.sum((num_np_array - grand_mean) ** 2)

                    if ss_total == 0:
                        continue

                    # Calculate sum of squares between groups
                    ss_between = 0
                    for lvl in levels:
                        mask = cat_np_array == lvl
                        if np.any(
                            mask
                        ):  # Check if we have any values for this level
                            group_mean = np.mean(num_np_array[mask])
                            ss_between += (
                                np.sum(mask) * (group_mean - grand_mean) ** 2
                            )

                    eta_sq = ss_between / ss_total
                    if np.isfinite(eta_sq):
                        results[(catcol, numcol)] = {
                            "correlation_eta_squared_coef": round(
                                float(eta_sq), 4
                            )
                        }
            except Exception as e:
                logger.warning(
                    f"Could not compute numeric-categorical correlation between {catcol} and {numcol}: {str(e)}"
                )
                continue

    # 3. categorical - categorical (Cramer's V)
    for i, col1 in enumerate(categorical_cols):
        for col2 in categorical_cols[i + 1 :]:
            try:
                sub = df.select([col1, col2]).drop_nulls().collect()
                if sub.height < 2:
                    continue

                # Convert to pandas for crosstab
                cont = pd.crosstab(sub[col1].to_pandas(), sub[col2].to_pandas())

                if cont.shape[0] < 2 or cont.shape[1] < 2:
                    continue

                chi2, _, _, _ = chi2_contingency(cont, correction=False)
                n = float(cont.values.sum())

                # Calculate Cramer's V
                r, k = cont.shape
                min_dim = float(min(k - 1, r - 1))
                if min_dim > 0:  # Avoid division by zero
                    v = np.sqrt(chi2 / (n * min_dim))  # type: ignore
                    if np.isfinite(v):
                        results[(col1, col2)] = {
                            "correlation_cramer_v_coef": round(float(v), 4)
                        }
            except Exception as e:
                logger.warning(
                    f"Could not compute Cramer's V between {col1} and {col2}: {str(e)}"
                )
                continue

    logger.debug(
        f"Finished correlation analysis. Found {len(results)} correlations."
    )
    return results


def create_pivot_table(
    self,
    df: pl.LazyFrame,
    index: Union[str, List[str]],
    columns: Optional[Union[str, List[str]]] = None,
    values: Optional[Union[str, List[str]]] = None,
    aggfunc: Union[str, List[str]] = "mean",
    fill_value: Optional[Any] = None,
    margins: bool = False,
) -> Dict[str, Any]:
    """
    Create separate pivot tables for each index-column combination.
    Each pivot table will have at most one index and one column.
    Also calculates and ranks the variance of indexes to show their influence on values.

    Args:
        df: Input LazyFrame
        index: Column(s) to use as index
        columns: Column(s) to use for creating multiple columns. If None, only index will be used.
        values: Column(s) to aggregate. If None, will use all numeric columns
        aggfunc: Aggregation function(s) to use. Can be:
                - string: 'sum', 'mean', 'median', 'min', 'max', 'count', 'std', 'var', 'first', 'last'
                - list of strings: will apply multiple aggregations
        fill_value: Value to fill in missing cells
        margins: Whether to include row/column totals

    Returns:
        Dictionary containing:
            - 'pivot_tables': List of pivot tables
            - 'summary': Summary statistics about the pivot operation
            - 'shapes': Shape of the resulting pivot tables
            - 'index_rankings': For each value column, ranked indexes by their variance
    """
    try:
        logger.debug("=== Starting pivot table creation ===")
        logger.debug("Input parameters:")
        logger.debug(f"- index: {index}")
        logger.debug(f"- columns: {columns}")
        logger.debug(f"- values: {values}")
        logger.debug(f"- aggfunc: {aggfunc}")

        # Convert inputs to lists
        index_cols = self._as_list(index)
        column_cols = self._as_list(columns) if columns else []
        agg_funcs = self._as_list(aggfunc)

        if not agg_funcs:
            raise ValueError("`aggfunc` must not be empty")

        # If values is None, use all numeric columns
        schema = df.collect_schema()
        if values is None:
            values = [
                col
                for col, dtype in schema.items()
                if dtype.is_numeric() and col not in index_cols + column_cols
            ]
            if not values:
                raise ValueError("No numeric columns found for aggregation")
            logger.debug(f"Auto-selected numeric columns for values: {values}")
        value_cols = self._as_list(values)

        # Validate all columns exist
        for col in (*index_cols, *column_cols, *value_cols):
            if col not in schema:
                raise ValueError(f"column '{col}' not in dataframe")

        # Cast index columns to string type to ensure proper handling
        for idx in index_cols:
            df = df.with_columns(pl.col(idx).cast(pl.Utf8).alias(idx))

        # Validate aggregation functions
        invalid_funcs = [f for f in agg_funcs if f not in AGGREGATION_FUNCTIONS]
        if invalid_funcs:
            raise ValueError(f"Invalid aggregation functions: {invalid_funcs}")

        pivot_tables: List[Dict[str, Any]] = []
        index_rankings: Dict[str, Dict[str, float]] = {}

        # Calculate index rankings for each value column
        for val in value_cols:
            index_variances: Dict[str, float] = {}

            for idx in index_cols:
                # Create pivot table with single index
                pivoted = (
                    df.group_by(idx)
                    .agg(
                        AGGREGATION_FUNCTIONS[agg_funcs[0]](val).alias(
                            "agg_value"
                        )
                    )
                    .collect()
                )

                # Calculate variance
                agg_values: List[float] = pivoted["agg_value"].to_list()
                mean = sum(agg_values) / len(agg_values)
                variance = sum((x - mean) ** 2 for x in agg_values) / len(
                    agg_values
                )

                index_variances[idx] = variance

            # Sort indexes by variance in descending order
            sorted_variances = dict(
                sorted(
                    index_variances.items(),
                    key=lambda x: x[1],
                    reverse=True,
                )
            )

            index_rankings[val] = sorted_variances

        # If no columns specified, create pivot tables with only index
        if not column_cols:
            for idx in index_cols:
                for val in value_cols:
                    for func in agg_funcs:
                        agg_name = f"{val}_{func}_by_{idx}"
                        logger.debug(f"\nProcessing {agg_name}")

                        # Create pivot table with single index
                        pivoted = (
                            df.group_by(idx)
                            .agg(
                                AGGREGATION_FUNCTIONS[func](val).alias(agg_name)
                            )
                            .collect()
                        )

                        if fill_value is not None:
                            pivoted = pivoted.fill_null(fill_value)

                        if margins:
                            total_row = {
                                idx: "Total",
                                agg_name: pivoted.select(pl.col(agg_name))
                                .mean()
                                .item(),
                            }
                            pivoted = pl.concat(
                                [pivoted, pl.DataFrame([total_row])]
                            )

                        pivot_tables.append(
                            {
                                "name": agg_name,
                                "index_names": [idx],
                                "column_names": [agg_name],
                                "index_values": [
                                    [x] for x in pivoted[idx].to_list()
                                ],
                                "data_values": [
                                    [x] for x in pivoted[agg_name].to_list()
                                ],
                            }
                        )
        else:
            # Create separate pivot tables for each index-column combination
            for idx in index_cols:
                for col in column_cols:
                    for val in value_cols:
                        for func in agg_funcs:
                            agg_name = f"{val}_{func}_by_{idx}_and_{col}"
                            logger.debug(f"\nProcessing {agg_name}")

                            # Create pivot table with single index and single column
                            pivoted = df.collect().pivot(
                                on=[col],
                                index=[idx],
                                values=val,
                                aggregate_function=func,  # type: ignore
                            )

                            if fill_value is not None:
                                pivoted = pivoted.fill_null(fill_value)

                            if margins:
                                # Add column totals
                                pivoted = pivoted.with_columns(
                                    pl.sum_horizontal(
                                        pl.all().exclude(idx)
                                    ).alias("Total")
                                )
                                # Add row totals
                                total_row = {
                                    idx: "Total",
                                    **{
                                        c: pivoted.select(pl.col(c))
                                        .mean()
                                        .item()
                                        for c in pivoted.columns
                                        if c != idx
                                    },
                                }

                                pivoted = pl.concat(
                                    [pivoted, pl.DataFrame([total_row])],
                                    how="vertical_relaxed",
                                )

                            pivot_tables.append(
                                {
                                    "name": agg_name,
                                    "index_names": [idx],
                                    "column_names": [
                                        c for c in pivoted.columns if c != idx
                                    ],
                                    "index_values": [
                                        [x] for x in pivoted[idx].to_list()
                                    ],
                                    "data_values": [
                                        [
                                            row[c]
                                            for c in pivoted.columns
                                            if c != idx
                                        ]
                                        for row in pivoted.to_dicts()
                                    ],
                                }
                            )

        shapes = {
            d["name"]: (len(d["index_values"]), len(d["column_names"]))
            for d in pivot_tables
        }

        summary = {
            "num_index_levels": len(index_cols),
            "num_column_levels": len(column_cols),
            "num_value_columns": len(value_cols),
            "aggregation_functions": agg_funcs,
            "has_margins": margins,
        }

        return {
            "pivot_tables": pivot_tables,
            "summary": summary,
            "shapes": shapes,
            "index_rankings": index_rankings,
        }
    except Exception as e:
        logger.exception("create_pivot_table failed")
        raise ValueError(f"error creating pivot table: {e}") from e
