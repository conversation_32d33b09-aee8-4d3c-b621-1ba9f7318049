from enum import Enum
from typing import Any, Dict, Optional

import pandas as pd
import polars as pl
from langgraph_da_backend.utils.loader.post_cast import (
    _post_cast_currency_cols,
    _post_cast_datetime_cols,
)
from pydantic import BaseModel, model_validator
from sqlalchemy import create_engine
from sqlalchemy.engine import URL


class SupportedDBDialect(str, Enum):
    """
    An enumeration of common database dialects supported by SQLAlchemy.
    """

    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"
    ORACLE = "oracle"
    MSSQL = "mssql"
    MARIADB = "mariadb"


class DBConfigBase(BaseModel):
    """Base configuration for database connections, handling automatic driver selection."""

    db_dialect: SupportedDBDialect
    db_driver: Optional[str] = None

    @model_validator(mode="before")
    @classmethod
    def auto_select_driver(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """
        Automatically selects a default driver if one is not provided.
        This runs before model validation.
        """
        dialect = values.get("db_dialect")
        driver = values.get("db_driver")

        if dialect and not driver:
            driver_map = {
                SupportedDBDialect.MYSQL: "mysqlconnector",
                SupportedDBDialect.POSTGRESQL: "psycopg2",
                SupportedDBDialect.SQLITE: "pysqlite",
                SupportedDBDialect.ORACLE: "oracledb",
                SupportedDBDialect.MSSQL: "pyodbc",
                SupportedDBDialect.MARIADB: "mysqlconnector",
            }
            # Set the default driver in the input values
            values["db_driver"] = driver_map.get(dialect)

        # Ensure a driver is set for dialects that need one
        if dialect and not values.get("db_driver"):
            raise ValueError(
                f"Could not automatically determine a driver for dialect '{dialect}'. Please specify a 'db_driver'."
            )

        return values

    def get_engine(self):
        """
        Creates and returns a SQLAlchemy engine based on the configuration.
        """
        raise NotImplementedError


class LocalDBConfig(DBConfigBase):
    """
    Configuration for local database connections (e.g., SQLite).
    """

    db_path: str

    def get_engine(self):
        """
        Constructs the SQLAlchemy engine URL for a local database and returns the engine.
        """
        url = URL.create(
            drivername=f"{self.db_dialect}+{self.db_driver}",
            database=self.db_path,
        )
        return create_engine(url)


class RemoteDBConfig(DBConfigBase):
    """
    Configuration for remote database connections.
    """

    db_host: str
    db_port: int
    db_username: str
    db_password: str
    db_name: str

    def get_engine(self):
        """
        Constructs the SQLAlchemy engine URL for a remote database and returns the engine.
        """
        url = URL.create(
            drivername=f"{self.db_dialect}+{self.db_driver}",
            username=self.db_username,
            password=self.db_password,
            host=self.db_host,
            port=self.db_port,
            database=self.db_name,
        )
        return create_engine(url)


def load(
    config: LocalDBConfig | RemoteDBConfig,
    table_name: Optional[str] = None,
    sql_query: Optional[str] = None,
) -> pl.LazyFrame:
    """
    Load data from a database table or execute a SQL query and return a polars LazyFrame.

    Args:
        config (LocalDBConfig | RemoteDBConfig): Configuration for the database connection.
        table_name (str, optional): Name of the table to load. Defaults to None.
        sql_query (str, optional): SQL query to execute. Defaults to None.

    Returns:
        pl.LazyFrame: A polars LazyFrame containing the loaded data.
    """
    if not (table_name or sql_query):
        raise ValueError("Either 'table_name' or 'sql_query' must be provided.")

    try:
        engine = config.get_engine()
        with engine.connect() as conn:
            if table_name:
                df_pd = pd.read_sql_table(table_name, conn)
            elif sql_query:
                df_pd = pd.read_sql_query(sql_query, conn)
            else:
                raise ValueError(
                    "Either 'table_name' or 'sql_query' must be provided."
                )
        lf = pl.from_pandas(df_pd).lazy()
        lf = _post_cast_datetime_cols(lf)
        lf = _post_cast_currency_cols(lf)
        return lf

    except Exception as e:
        raise Exception(f"Failed to load data from database: {e}")
