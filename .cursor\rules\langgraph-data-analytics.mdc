---
description: 
globs: 
alwaysApply: false
---
---
description: "Guidelines and workflow conventions for the LangGraph DA backend — covers the agentic data-analysis stages, coding constraints, and tool behavior."
alwaysApply: true
---
## Current Target
Create tool cards for data analytics. One example is like below:

@register_tool
@tool_card(keywords=["example", "test", "dev"])
def example_function(
    a: Annotated[int, "The first number"],
    meaningful_para: str,
    complex_structure: Annotated[
        dict[str | int],
        "A complex structure which does not have fix keys",
    ],
    default_para: float = 3.14,
):
    """
    This is an example function with a docstring and annotations.
    This function actually does nothing and is non-sense, so never pick it as a real tool.
    """
    pass




## 🔧 Coding / Tool Constraints
- **Do NOT modify** `pyproject.toml` or `requirements.txt` directly.
- Use `uv add <package>` for dependencies — avoid manual edits to lock files.
- All code-generation must respect this dependency rule.


