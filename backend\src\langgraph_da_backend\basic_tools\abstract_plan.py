import json
import os
from pprint import pprint
from typing import List, Literal

from langchain.chat_models import init_chat_model
from langchain_core.output_parsers import JsonOutputParser
from langgraph_da_backend.basic_tools.tool_schema import DataProcessingPipeline
from pydantic import BaseModel, Field

abstract_plan_template = """
You are an expert data scientist, and you are helping the user to address their data analysis request.
The raw request is:
'{user_raw_request}'

A descriptive analysis of the data has been performed and is as follows:
{descriptive_analysis}

Your task is to restate the user's original request into a formal data analysis problem, and propose a clear analysis plan.
Follow the step-by-step instruction:
1. Determine the intention of user request and select the task type.
2. Eliminate the ambiguities and misusage of terms in user's problem, and refine the problem description.
3. Based on the task type and refined problem, formulate a brief plan to solve the user's problem. The plan includes two parts: how to data-engineering the original data to prepare it for further analysis, and what standard data analysis methods / models to use, which columns to operate on, and how to perform the analysis.

The output must strictly follow the following json schema: {json_schema}
"""

DA_TASK_TYPES = Literal[
    "descriptive",
    "diagnostic",
    "predictive",
    "prescriptive",
]


class RequestSanityCheck(BaseModel):
    is_da_request: bool = Field(
        description="Whether the request indicates a data analysis request or not."
    )


class AbstractPlan(BaseModel):
    task_type: List[DA_TASK_TYPES] = Field(
        description="The task type of the user's request. "
    )
    refined_request: str = Field(
        description="The refined request that eliminates the ambiguities and misusage of terms in user's problem."
    )
    data_engineering_plan: str = Field(
        description="The data-engineering plan that mainly states how to data-engineering the original data to prepare it for further analysis."
    )
    analysis_plan: str = Field(
        description="The proposed analysis plan that mainly states the standard data analysis methods / models to use, which columns to operate on, and how to perform the analysis."
    )
