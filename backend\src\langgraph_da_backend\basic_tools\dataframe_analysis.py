import math
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, List, Literal, Optional, Tuple, Union, Annotated, Callable
import json

import polars as pl  # type: ignore
from ..utils.tool.tool_card import tool_card, register_tool

# Import the new, modular utility functions
_post_cast_datetime_cols: Callable
_post_cast_currency_cols: Callable
try:
    from ..utils.loader.post_cast import _post_cast_datetime_cols, _post_cast_currency_cols
    INTELLIGENT_LOADER_AVAILABLE = True
except ImportError:
    # Assign a no-op lambda to satisfy the type checker if import fails
    _post_cast_datetime_cols = lambda df, sample_size=100: df
    _post_cast_currency_cols = lambda df, sample_size=100: df
    INTELLIGENT_LOADER_AVAILABLE = False


def _detect_column_types(
    df: pl.LazyFrame,
    categorical_threshold: float = 0.2,
) -> Dict[str, List[str]]:
    """
    Detects the data types of each column in a DataFrame, copied from core/utils.py
    and adapted to remove external dependencies.
    """
    df_collected = df.collect()
    df_len = df_collected.height
    schema = df_collected.schema

    if df_len == 0:
        return {
            "numeric": [name for name, dtype in schema.items() if dtype.is_numeric()],
            "text": [name for name, dtype in schema.items() if dtype in (pl.String, pl.Utf8)],
            "categorical": [name for name, dtype in schema.items() if dtype == pl.Categorical],
            "datetime": [name for name, dtype in schema.items() if dtype.is_temporal()],
            "undefined": [],
        }

    df_unique_dict = {
        key: value[0]
        for key, value in df.select(
            [pl.col(col).n_unique().alias(col) for col in schema.names()]
        ).collect().to_dict().items()
    }

    unique_ratio = {key: value / df_len for key, value in df_unique_dict.items()}

    categorical_cols = [
        name
        for name, dtype in schema.items()
        if dtype in (pl.Categorical, pl.Boolean, pl.Enum)
    ]
    categorical_cols += [
        name
        for name, ratio in unique_ratio.items()
        if (ratio <= categorical_threshold or df_unique_dict[name] <= 3)
        and not (schema[name].is_float() or schema[name].is_decimal() or schema[name].is_temporal())
    ]
    categorical_cols = list(set(categorical_cols))

    numeric_cols = [
        name
        for name, dtype in schema.items()
        if dtype.is_numeric() and name not in categorical_cols
    ]
    text_cols = [
        name
        for name, dtype in schema.items()
        if dtype in (pl.Utf8, pl.String) and name not in categorical_cols
    ]
    datetime_cols = [
        name
        for name, dtype in schema.items()
        if dtype.is_temporal() and name not in categorical_cols
    ]

    return {
        "numeric": numeric_cols,
        "text": text_cols,
        "categorical": categorical_cols,
        "datetime": datetime_cols,
        "undefined": [
            name
            for name in schema.names()
            if name not in (categorical_cols + numeric_cols + text_cols + datetime_cols)
        ],
    }


def _load_lazyframe_from_input(df_data: str, auto_detect_types: bool = False) -> "pl.LazyFrame":
    """Helper to load a LazyFrame from a file path or JSON string."""
    if df_data.endswith('.csv') or df_data.endswith('.parquet'):
        if df_data.endswith('.csv'):
            lf = pl.scan_csv(df_data, null_values=["null", "None", "NULL", "nan", "NaN", ""])
        else:
            lf = pl.scan_parquet(df_data)
        
        if auto_detect_types and INTELLIGENT_LOADER_AVAILABLE:
            lf = _post_cast_datetime_cols(lf)
            lf = _post_cast_currency_cols(lf)
        return lf
    else:
        try:
            data = json.loads(df_data)
            return pl.LazyFrame(data)
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format in df_data parameter")


@register_tool
@tool_card(
    keywords=["dataframe", "analysis", "statistics", "describe", "summary", "comprehensive"],
    description="Performs comprehensive statistical analysis on a DataFrame including descriptive statistics, data type detection, and data quality assessment"
)
def analyze_dataframe(
    df_data: Annotated[str, "File path to CSV/Parquet file or JSON string representation of DataFrame data"],
    categorical_threshold: Annotated[float, "Threshold for determining categorical vs numeric columns"] = 0.2
) -> Dict[str, Any]:
    """
    Performs comprehensive analysis on a DataFrame including:
    - Basic statistics (rows, duplicates, missing values)
    - Automatic data type detection (numeric, categorical, text, datetime)
    - Column-specific analysis based on detected types
    - Data quality assessment
    
    Returns a detailed analysis report as a dictionary.
    """
    try:
        # Load data using the helper function with intelligent casting enabled
        df = _load_lazyframe_from_input(df_data, auto_detect_types=True)
        
        # Basic DataFrame statistics
        df_len = int(df.select(pl.len()).collect().item())
        df_dup_len = int(df.select(pl.len()).collect().item() - df.unique().select(pl.len()).collect().item())
        
        # Missing values analysis
        missing_dict = {
            key: value[0]
            for key, value in df.select([
                pl.col(col).is_null().sum().alias(col)
                for col in df.collect_schema().names()
            ]).collect().to_dict().items()
        }
        
        # Unique values analysis
        unique_dict = {
            key: value[0]
            for key, value in df.select([
                pl.col(col).n_unique().alias(col)
                for col in df.collect_schema().names()
            ]).collect().to_dict().items()
        }
        
        # Column type detection using local utility
        detailed_column_analysis = {}
        try:
            type_detection = _detect_column_types(df, categorical_threshold)
            column_types: Dict[str, Union[List[str], str]] = {
                "numeric": type_detection.get("numeric", []),
                "categorical": type_detection.get("categorical", []),
                "text": type_detection.get("text", []),
                "datetime": type_detection.get("datetime", []),
                "undefined": type_detection.get("undefined", [])
            }
            
            # Perform detailed analysis for each column type
            for col in type_detection.get("numeric", []):
                detailed_column_analysis[col] = _analyze_numeric_column(df, col)
            
            for col in type_detection.get("categorical", []):
                detailed_column_analysis[col] = _analyze_categorical_column(df, col)
            
            for col in type_detection.get("text", []):
                detailed_column_analysis[col] = _analyze_text_column(df, col)
            
            for col in type_detection.get("datetime", []):
                detailed_column_analysis[col] = _analyze_datetime_column(df, col)
                
        except Exception as e:
            column_types = {"error": f"Column type detection failed: {str(e)}"}
        
        # Data quality metrics
        data_quality = _assess_data_quality(df, missing_dict, unique_dict, df_len)
        
        return {
            "basic_stats": {
                "num_rows": df_len,
                "num_columns": len(df.collect_schema().names()),
                "num_duplicate_rows": df_dup_len,
                "duplicate_percentage": round((df_dup_len / df_len * 100), 2) if df_len > 0 else 0
            },
            "missing_values": missing_dict,
            "unique_values": unique_dict,
            "column_types": column_types,
            "detailed_column_analysis": detailed_column_analysis,
            "data_quality": data_quality,
            "analysis_summary": "Comprehensive DataFrame analysis completed successfully"
        }
        
    except Exception as e:
        return {"error": f"Error performing analysis: {str(e)}"}


def _analyze_numeric_column(df, col_name: str) -> Dict[str, Any]:
    """Helper function to analyze numeric columns."""
    try:
        stats = df.select([
            pl.col(col_name).min().alias("min"),
            pl.col(col_name).max().alias("max"),
            pl.col(col_name).mean().alias("mean"),
            pl.col(col_name).median().alias("median"),
            pl.col(col_name).std().alias("std"),
            pl.col(col_name).quantile(0.25).alias("q1"),
            pl.col(col_name).quantile(0.75).alias("q3"),
            pl.col(col_name).quantile(0.05).alias("p5"),
            pl.col(col_name).quantile(0.95).alias("p95")
        ]).collect()
        
        result = {
            "type": "numeric",
            "min": stats["min"][0],
            "max": stats["max"][0],
            "mean": stats["mean"][0],
            "median": stats["median"][0],
            "std": stats["std"][0],
            "q1": stats["q1"][0],
            "q3": stats["q3"][0],
            "p5": stats["p5"][0],
            "p95": stats["p95"][0]
        }
        
        # Derived statistics
        if result["max"] is not None and result["min"] is not None:
            result["range"] = result["max"] - result["min"]
        if result["q3"] is not None and result["q1"] is not None:
            result["iqr"] = result["q3"] - result["q1"]
        if result["std"] is not None and result["mean"] is not None and result["mean"] != 0:
            result["coefficient_of_variation"] = result["std"] / result["mean"]
        
        # Outlier detection using IQR method
        if result["q1"] is not None and result["q3"] is not None and result["iqr"] is not None:
            lower_bound = result["q1"] - 1.5 * result["iqr"]
            upper_bound = result["q3"] + 1.5 * result["iqr"]
            outlier_count = df.filter(
                (pl.col(col_name) < lower_bound) | (pl.col(col_name) > upper_bound)
            ).select(pl.len()).collect().item()
            result["outlier_count"] = outlier_count
            result["outlier_bounds"] = {"lower": lower_bound, "upper": upper_bound}
        
        return result
        
    except Exception as e:
        return {"type": "numeric", "error": f"Error analyzing numeric column: {str(e)}"}


def _analyze_categorical_column(df, col_name: str) -> Dict[str, Any]:
    """Helper function to analyze categorical columns."""
    try:
        value_counts = (
            df.group_by(col_name)
            .agg(pl.len().alias("count"))
            .sort("count", descending=True)
            .head(10)
            .collect()
        )
        
        total_count = df.select(pl.len()).collect().item()
        
        top_values = {}
        for row in value_counts.to_dicts():
            value = row[col_name]
            count = row["count"]
            percentage = (count / total_count * 100) if total_count > 0 else 0
            top_values[str(value)] = {"count": count, "percentage": round(percentage, 2)}
        
        return {
            "type": "categorical",
            "top_values": top_values,
            "num_categories": len(value_counts)
        }
        
    except Exception as e:
        return {"type": "categorical", "error": f"Error analyzing categorical column: {str(e)}"}


def _analyze_text_column(df, col_name: str) -> Dict[str, Any]:
    """Helper function to analyze text columns."""
    try:
        lengths = df.select(
            pl.col(col_name).filter(pl.col(col_name).is_not_null()).str.len_chars().alias("lengths")
        ).collect()["lengths"].drop_nulls()
        
        if len(lengths) == 0:
            return {"type": "text", "error": "No non-null text values found"}
        
        result = {
            "type": "text",
            "min_length": lengths.min(),
            "max_length": lengths.max(),
            "mean_length": lengths.mean(),
            "median_length": lengths.median(),
            "total_entries": len(lengths)
        }
        
        # Check if text might actually be categorical (low unique ratio)
        unique_count = df.select(pl.col(col_name).n_unique()).collect().item()
        total_count = df.select(pl.len()).collect().item()
        unique_ratio = unique_count / total_count if total_count > 0 else 0
        result["unique_ratio"] = round(unique_ratio, 4)
        
        if unique_ratio < 0.1:  # Might be categorical
            result["suggestion"] = "Consider treating as categorical due to low unique ratio"
        
        return result
        
    except Exception as e:
        return {"type": "text", "error": f"Error analyzing text column: {str(e)}"}


def _analyze_datetime_column(df, col_name: str) -> Dict[str, Any]:
    """Helper function to analyze datetime columns."""
    try:
        stats = df.select([
            pl.col(col_name).min().alias("min_date"),
            pl.col(col_name).max().alias("max_date"),
            pl.col(col_name).count().alias("count")
        ]).collect()
        
        min_date = stats["min_date"][0]
        max_date = stats["max_date"][0]
        
        result = {
            "type": "datetime",
            "earliest_date": str(min_date) if min_date else None,
            "latest_date": str(max_date) if max_date else None,
            "count": stats["count"][0]
        }
        
        if min_date and max_date:
            time_span = max_date - min_date
            result["time_span_days"] = time_span.days if hasattr(time_span, 'days') else str(time_span)
        
        return result
        
    except Exception as e:
        return {"type": "datetime", "error": f"Error analyzing datetime column: {str(e)}"}


def _assess_data_quality(df, missing_dict: Dict, unique_dict: Dict, total_rows: int) -> Dict[str, Any]:
    """Assess overall data quality metrics."""
    try:
        total_cells = total_rows * len(df.collect_schema().names())
        total_missing = sum(missing_dict.values())
        
        quality_metrics = {
            "completeness_score": round((1 - total_missing / total_cells) * 100, 2) if total_cells > 0 else 100,
            "total_missing_cells": total_missing,
            "columns_with_missing": sum(1 for v in missing_dict.values() if v > 0),
            "high_cardinality_columns": [
                col for col, unique_count in unique_dict.items() 
                if unique_count / total_rows > 0.95 if total_rows > 0
            ],
            "low_cardinality_columns": [
                col for col, unique_count in unique_dict.items() 
                if unique_count / total_rows < 0.05 if total_rows > 0
            ]
        }
        
        # Quality assessment
        if quality_metrics["completeness_score"] >= 95:
            quality_metrics["overall_quality"] = "Excellent"
        elif quality_metrics["completeness_score"] >= 85:
            quality_metrics["overall_quality"] = "Good"
        elif quality_metrics["completeness_score"] >= 70:
            quality_metrics["overall_quality"] = "Fair"
        else:
            quality_metrics["overall_quality"] = "Poor"
        
        return quality_metrics
        
    except Exception as e:
        return {"error": f"Error assessing data quality: {str(e)}"}


@register_tool
@tool_card(
    keywords=["report", "markdown", "format", "analysis", "summary", "readable"],
    description="Formats comprehensive analysis results into a detailed Markdown report"
)
def format_analysis_report(
    analysis_data: Annotated[Dict[str, Any], "Dictionary containing analysis results from analyze_dataframe"]
) -> str:
    """
    Converts comprehensive analysis results into a well-formatted Markdown report.
    
    The report includes:
    - Executive summary
    - Basic statistics
    - Data quality assessment
    - Column-by-column analysis
    - Recommendations
    """
    try:
        report_parts = []
        
        # Title and Executive Summary
        report_parts.append("# DataFrame Analysis Report\n\n")
        report_parts.append("## Executive Summary\n\n")
        
        basic_stats = analysis_data.get("basic_stats", {})
        data_quality = analysis_data.get("data_quality", {})
        
        report_parts.append(f"- **Dataset Size:** {basic_stats.get('num_rows', 'N/A')} rows × {basic_stats.get('num_columns', 'N/A')} columns\n")
        report_parts.append(f"- **Data Quality:** {data_quality.get('overall_quality', 'N/A')}\n")
        report_parts.append(f"- **Completeness:** {data_quality.get('completeness_score', 'N/A')}%\n")
        report_parts.append(f"- **Duplicate Rows:** {basic_stats.get('num_duplicate_rows', 'N/A')} ({basic_stats.get('duplicate_percentage', 'N/A')}%)\n\n")
        
        # Basic Statistics Section
        report_parts.append("## Basic Statistics\n\n")
        report_parts.append(f"| Metric | Value |\n")
        report_parts.append(f"|--------|-------|\n")
        report_parts.append(f"| Total Rows | {basic_stats.get('num_rows', 'N/A')} |\n")
        report_parts.append(f"| Total Columns | {basic_stats.get('num_columns', 'N/A')} |\n")
        report_parts.append(f"| Duplicate Rows | {basic_stats.get('num_duplicate_rows', 'N/A')} |\n")
        report_parts.append(f"| Missing Cells | {data_quality.get('total_missing_cells', 'N/A')} |\n")
        report_parts.append(f"| Columns with Missing Values | {data_quality.get('columns_with_missing', 'N/A')} |\n\n")
        
        # Column Type Distribution
        column_types = analysis_data.get("column_types", {})
        if isinstance(column_types, dict) and "error" not in column_types:
            report_parts.append("## Column Type Distribution\n\n")
            for type_name, columns in column_types.items():
                if columns:
                    report_parts.append(f"- **{type_name.title()}:** {len(columns)} columns ({', '.join(f'`{col}`' for col in columns[:5])}{'...' if len(columns) > 5 else ''})\n")
            report_parts.append("\n")
        
        # Detailed Column Analysis
        detailed_analysis = analysis_data.get("detailed_column_analysis", {})
        if detailed_analysis:
            report_parts.append("## Detailed Column Analysis\n\n")
            
            for col_name, analysis in detailed_analysis.items():
                report_parts.append(f"### Column: `{col_name}`\n\n")
                col_type = analysis.get("type", "unknown")
                report_parts.append(f"**Type:** {col_type.title()}\n\n")
                
                if "error" in analysis:
                    report_parts.append(f"**Error:** {analysis['error']}\n\n")
                    continue
                
                # Type-specific reporting
                if col_type == "numeric":
                    _add_numeric_analysis_to_report(report_parts, analysis)
                elif col_type == "categorical":
                    _add_categorical_analysis_to_report(report_parts, analysis)
                elif col_type == "text":
                    _add_text_analysis_to_report(report_parts, analysis)
                elif col_type == "datetime":
                    _add_datetime_analysis_to_report(report_parts, analysis)
                
                report_parts.append("\n")
        
        # Data Quality Issues and Recommendations
        if data_quality:
            report_parts.append("## Data Quality Assessment\n\n")
            _add_quality_assessment_to_report(report_parts, data_quality)
        
        return "".join(report_parts)
        
    except Exception as e:
        return f"Error formatting report: {str(e)}"


def _add_numeric_analysis_to_report(report_parts: List[str], analysis: Dict[str, Any]):
    """Add numeric column analysis to report."""
    report_parts.append("**Statistics:**\n")
    report_parts.append(f"- Min: {analysis.get('min', 'N/A')}\n")
    report_parts.append(f"- Max: {analysis.get('max', 'N/A')}\n")
    report_parts.append(f"- Mean: {analysis.get('mean', 'N/A')}\n")
    report_parts.append(f"- Median: {analysis.get('median', 'N/A')}\n")
    report_parts.append(f"- Std Dev: {analysis.get('std', 'N/A')}\n")
    report_parts.append(f"- IQR: {analysis.get('iqr', 'N/A')}\n")
    
    if 'outlier_count' in analysis:
        report_parts.append(f"- Outliers (IQR method): {analysis['outlier_count']}\n")


def _add_categorical_analysis_to_report(report_parts: List[str], analysis: Dict[str, Any]):
    """Add categorical column analysis to report."""
    report_parts.append(f"**Categories:** {analysis.get('num_categories', 'N/A')}\n\n")
    report_parts.append("**Top Values:**\n")
    
    top_values = analysis.get('top_values', {})
    for value, stats in list(top_values.items())[:5]:
        count = stats.get('count', 0)
        percentage = stats.get('percentage', 0)
        report_parts.append(f"- `{value}`: {count} ({percentage}%)\n")


def _add_text_analysis_to_report(report_parts: List[str], analysis: Dict[str, Any]):
    """Add text column analysis to report."""
    report_parts.append("**Text Statistics:**\n")
    report_parts.append(f"- Min Length: {analysis.get('min_length', 'N/A')}\n")
    report_parts.append(f"- Max Length: {analysis.get('max_length', 'N/A')}\n")
    report_parts.append(f"- Mean Length: {analysis.get('mean_length', 'N/A')}\n")
    report_parts.append(f"- Unique Ratio: {analysis.get('unique_ratio', 'N/A')}\n")
    
    if 'suggestion' in analysis:
        report_parts.append(f"- **Suggestion:** {analysis['suggestion']}\n")


def _add_datetime_analysis_to_report(report_parts: List[str], analysis: Dict[str, Any]):
    """Add datetime column analysis to report."""
    report_parts.append("**Date Range:**\n")
    report_parts.append(f"- Earliest: {analysis.get('earliest_date', 'N/A')}\n")
    report_parts.append(f"- Latest: {analysis.get('latest_date', 'N/A')}\n")
    report_parts.append(f"- Span: {analysis.get('time_span_days', 'N/A')} days\n")


def _add_quality_assessment_to_report(report_parts: List[str], data_quality: Dict[str, Any]):
    """Add data quality assessment to report."""
    report_parts.append(f"**Overall Quality Score:** {data_quality.get('overall_quality', 'N/A')}\n\n")
    
    high_cardinality = data_quality.get('high_cardinality_columns', [])
    low_cardinality = data_quality.get('low_cardinality_columns', [])
    
    if high_cardinality:
        report_parts.append("**High Cardinality Columns** (may contain unique identifiers):\n")
        for col in high_cardinality:
            report_parts.append(f"- `{col}`\n")
        report_parts.append("\n")
    
    if low_cardinality:
        report_parts.append("**Low Cardinality Columns** (potential categorical variables):\n")
        for col in low_cardinality:
            report_parts.append(f"- `{col}`\n")
        report_parts.append("\n")


# Keep the remaining simpler functions for backward compatibility
@register_tool
@tool_card(
    keywords=["missing", "values", "null", "data", "quality"],
    description="Analyzes missing values in each column of a DataFrame"
)
def get_missing_values_summary(
    df_data: Annotated[str, "File path or JSON string representation of DataFrame data"]
) -> Dict[str, Union[int, str]]:
    """
    Returns a dictionary with column names as keys and count of missing values as values.
    Useful for data quality assessment and cleaning decisions.
    """
    try:
        # Load data using the helper function
        df = _load_lazyframe_from_input(df_data)
        
        # Calculate missing values
        missing_dict = {
            key: value[0]
            for key, value in df.select([
                pl.col(col).is_null().sum().alias(col)
                for col in df.collect_schema().names()
            ]).collect().to_dict().items()
        }
        
        return missing_dict
        
    except Exception as e:
        return {"error": f"Error analyzing missing values: {str(e)}"}


@register_tool
@tool_card(
    keywords=["outliers", "anomaly", "detection", "statistics", "iqr"],
    description="Detects outliers in numeric columns using IQR method"
)
def detect_outliers(
    df_data: Annotated[str, "File path or JSON string representation of DataFrame data"],
    column_name: Annotated[str, "Name of the numeric column to analyze for outliers"],
    threshold: Annotated[float, "IQR threshold multiplier (typically 1.5)"] = 1.5
) -> Dict[str, Any]:
    """
    Detects outliers in a numeric column using the IQR method.
    
    Returns information about outliers including count and bounds.
    """
    try:
        # Load data using the helper function
        df = _load_lazyframe_from_input(df_data)
        
        # Calculate quartiles
        quantiles = df.select([
            pl.col(column_name).quantile(0.25).alias("q1"),
            pl.col(column_name).quantile(0.75).alias("q3")
        ]).collect()
        
        q1 = quantiles["q1"][0]
        q3 = quantiles["q3"][0]
        iqr = q3 - q1
        lower_bound = q1 - threshold * iqr
        upper_bound = q3 + threshold * iqr
        
        # Count outliers
        outlier_count = df.filter(
            (pl.col(column_name) < lower_bound) | (pl.col(column_name) > upper_bound)
        ).select(pl.len()).collect().item()
        
        return {
            "column": column_name,
            "outlier_count": outlier_count,
            "lower_bound": lower_bound,
            "upper_bound": upper_bound,
            "q1": q1,
            "q3": q3,
            "iqr": iqr,
            "threshold_used": threshold
        }
        
    except Exception as e:
        return {"error": f"Error detecting outliers: {str(e)}"}


@register_tool
@tool_card(
    keywords=["pivot", "table", "aggregation", "groupby", "summary"],
    description="Creates pivot tables with various aggregation functions"
)
def create_pivot_table(
    df_data: Annotated[str, "JSON string representation of the DataFrame data or file path"],
    index: Annotated[str, "Column to use as index/rows"],
    values: Annotated[str, "Column to aggregate"],
    aggfunc: Annotated[str, "Aggregation function: sum, mean, median, min, max, count"] = "mean"
) -> Dict[str, Any]:
    """
    Creates a simple pivot table for data analysis.
    
    Returns a dictionary containing the pivot table data and summary information.
    """
    try:
        # Load data using the helper function
        df = _load_lazyframe_from_input(df_data)
        
        # Aggregation mapping
        agg_funcs = {
            "sum": pl.col(values).sum(),
            "mean": pl.col(values).mean(),
            "median": pl.col(values).median(),
            "min": pl.col(values).min(),
            "max": pl.col(values).max(),
            "count": pl.col(values).count(),
        }
        
        if aggfunc not in agg_funcs:
            raise ValueError(f"Unsupported aggregation function: {aggfunc}")
        
        # Create pivot table
        result = df.group_by(index).agg(agg_funcs[aggfunc].alias(f"{values}_{aggfunc}")).collect()
        
        return {
            "pivot_data": result.to_dicts(),
            "shape": result.shape,
            "index_column": index,
            "value_column": values,
            "aggregation": aggfunc
        }
        
    except Exception as e:
        return {"error": f"Error creating pivot table: {str(e)}"}


@register_tool
@tool_card(
    keywords=["duplicate", "rows", "data", "quality", "cleaning"],
    description="Counts the number of duplicate rows in a DataFrame"
)
def get_duplicate_rows_count(
    df_data: Annotated[str, "JSON string representation of the DataFrame data or file path"]
) -> Dict[str, Union[int, str]]:
    """
    Returns the count of duplicate rows in the DataFrame.
    Helps identify data quality issues and potential cleaning needs.
    """
    try:
        # Load data using the helper function
        df = _load_lazyframe_from_input(df_data)
        
        total_rows = df.select(pl.len()).collect().item()
        unique_rows = df.unique().select(pl.len()).collect().item()
        duplicate_count = total_rows - unique_rows
        
        return {
            "total_rows": total_rows,
            "unique_rows": unique_rows,
            "duplicate_rows": duplicate_count
        }
        
    except Exception as e:
        return {"error": f"Error counting duplicate rows: {str(e)}"}


@register_tool
@tool_card(
    keywords=["unique", "values", "cardinality", "distinct", "data"],
    description="Counts unique values in each column of a DataFrame"
)
def get_unique_values_count(
    df_data: Annotated[str, "JSON string representation of the DataFrame data or file path"]
) -> Dict[str, Union[int, str]]:
    """
    Returns a dictionary with column names as keys and count of unique values as values.
    Useful for understanding data cardinality and identifying potential categorical columns.
    """
    try:
        # Load data using the helper function
        df = _load_lazyframe_from_input(df_data)
        
        # Calculate unique values for each column
        unique_dict = {
            key: value[0]
            for key, value in df.select([
                pl.col(col).n_unique().alias(col)
                for col in df.collect_schema().names()
            ]).collect().to_dict().items()
        }
        
        return unique_dict
        
    except Exception as e:
        return {"error": f"Error counting unique values: {str(e)}"}


@register_tool
@tool_card(
    keywords=["numeric", "statistics", "descriptive", "column", "analysis"],
    description="Provides detailed descriptive statistics for a numeric column"
)
def describe_numeric_column(
    df_data: Annotated[str, "JSON string representation of the DataFrame data or file path"],
    column_name: Annotated[str, "Name of the numeric column to analyze"]
) -> Dict[str, Union[int, float, str]]:
    """
    Returns comprehensive descriptive statistics for a numeric column including:
    - min, max, range, percentiles
    - mean, median, standard deviation
    """
    try:
        # Load data using the helper function
        df = _load_lazyframe_from_input(df_data)
        
        # Calculate statistics
        stats = df.select([
            pl.col(column_name).min().alias("min"),
            pl.col(column_name).max().alias("max"),
            pl.col(column_name).mean().alias("mean"),
            pl.col(column_name).median().alias("median"),
            pl.col(column_name).std().alias("std"),
            pl.col(column_name).quantile(0.25).alias("q1"),
            pl.col(column_name).quantile(0.75).alias("q3")
        ]).collect()
        
        result = {
            "min": stats["min"][0],
            "max": stats["max"][0],
            "mean": stats["mean"][0],
            "median": stats["median"][0],
            "std": stats["std"][0],
            "q1": stats["q1"][0],
            "q3": stats["q3"][0]
        }
        
        # Calculate derived statistics
        result["range"] = result["max"] - result["min"]
        result["iqr"] = result["q3"] - result["q1"]
        result["cv"] = result["std"] / result["mean"] if result["mean"] != 0 else 0
        
        return result
        
    except Exception as e:
        return {"error": f"Error analyzing numeric column: {str(e)}"}


@register_tool
@tool_card(
    keywords=["text", "string", "analysis", "length", "column"],
    description="Analyzes text column characteristics including length statistics"
)
def describe_text_column(
    df_data: Annotated[str, "JSON string representation of the DataFrame data or file path"],
    column_name: Annotated[str, "Name of the text column to analyze"]
) -> Dict[str, Any]:
    """
    Returns text analysis including:
    - Character length statistics (min, max, mean, median)
    - Basic text characteristics
    """
    try:
        # Load data using the helper function
        df = _load_lazyframe_from_input(df_data)
        
        # Calculate text length statistics
        lengths = df.select(
            pl.col(column_name).filter(pl.col(column_name).is_not_null()).str.len_chars().alias("lengths")
        ).collect()["lengths"].drop_nulls()
        
        return {
            "min_length": lengths.min(),
            "max_length": lengths.max(),
            "mean_length": lengths.mean(),
            "median_length": lengths.median(),
            "total_entries": len(lengths)
        }
        
    except Exception as e:
        return {"error": f"Error analyzing text column: {str(e)}"} 