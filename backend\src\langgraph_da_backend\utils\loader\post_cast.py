import datetime
import re
from decimal import Decimal
from typing import Dict, Optional, Union

import polars as pl
from dateutil.parser import parse as dt_parse


def _post_cast_datetime_cols(df: pl.<PERSON>zy<PERSON>rame, sample_size: int = 100):
    """
    Post cast datetime cols from seemingly string to datetime.
    """
    schema = df.collect_schema()
    suspicious_cols = [
        col for col, dtype in schema.items() if dtype == pl.String()
    ]
    if not suspicious_cols:
        return df

    conversion_map: Dict[str, Union[pl.Date, pl.Datetime]] = {}

    for col in suspicious_cols:
        samples_series = (
            df.select(pl.col(col).cast(pl.String()))
            .head(sample_size)
            .drop_nulls()
            .collect()
            .get_column(col)
        )
        samples = [s for s in samples_series.to_list() if s is not None]
        success_count = 0
        has_time = False
        dayfirst = False
        for sample in samples:
            if not isinstance(
                sample, str
            ):  # Should not happen due to cast + drop_nulls + list comp
                continue
            try:
                parsed = dt_parse(sample)
                if parsed.time() != datetime.time(0, 0):
                    has_time = True
                # ! YMD formats should be always the same across rows.
                # ! if not, let the csv exporter go to hell.
                if parsed.day > 12 and sample.startswith(str(parsed.day)):
                    dayfirst = True
                success_count += 1
            except Exception:
                pass
        if success_count / sample_size > 0.8:
            date_type = pl.Datetime() if has_time else pl.Date()
            conversion_map[col] = date_type

    def str2datetime(s: str, date_type: Union[pl.Date, pl.Datetime]):
        try:
            if date_type == pl.Date():
                return (
                    dt_parse(s, dayfirst=dayfirst).date().strftime("%Y-%m-%d")
                )
            elif date_type == pl.Datetime():
                return dt_parse(s, dayfirst=dayfirst).strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
            else:
                raise ValueError(f"Unsupported date type {date_type}")
        except Exception:
            return None

    return df.with_columns(
        [
            (
                pl.col(col)
                .map_elements(
                    lambda x: str2datetime(x, data_type),
                    return_dtype=pl.String(),
                )
                .str.to_date()
                .alias(col)
                if data_type == pl.Date()
                else pl.col(col)
                .map_elements(
                    lambda x: str2datetime(x, data_type),
                    return_dtype=pl.String(),
                )
                .str.to_datetime()
                .alias(col)
            )
            for col, data_type in conversion_map.items()
        ]
    )


def _post_cast_currency_cols(df: pl.LazyFrame, sample_size: int = 100):
    """
    Post cast currency cols from seemingly string to proper numeric values.
    """
    # Define MAGNITUDE and regex patterns at class level for efficiency
    MAGNITUDE = {
        # Words
        "THOUSAND": 3,
        "K": 3,  # K is often used standalone or as part of a word
        "MILLION": 6,
        # "M": 6, # 'M' is ambiguous, handle separately if single char
        "MN": 6,
        "MM": 6,  # Often used in finance for millions
        "BILLION": 9,
        # "B": 9, # 'B' is ambiguous
        "BN": 9,
        "TRILLION": 12,
        # "T": 12, # 'T' is ambiguous
        "TR": 12,
    }

    # Currency symbols to look for
    _currency_symbols_pattern = r"[$€£¥₹₽]"  # Add more as needed: ¥, ₹, ₽ etc.

    # Magnitude words (case-insensitive, whole words)
    _magnitude_word_pattern = r"\b(?:" + "|".join(MAGNITUDE.keys()) + r")\b"

    # Regex to capture currency-like strings.
    # It's verbose for readability.
    _strict_currency_re = re.compile(
        rf"""
        ^                                       # Start of string
        \s*                                     # Optional leading whitespace
        (?P<currency_symbol>{_currency_symbols_pattern})? # Optional currency symbol
        \s*                                     # Optional space after symbol
        (?P<sign>[-+])?                         # Optional sign
        \s*                                     # Optional space after sign
        (?P<number_val>\d[\d,]*(?:\.\d+)?)      # Number part (digits, commas, optional decimal)
        (?:                                     # Optional non-capturing group for space before magnitude chars
            \s*                                 # Optional space IF it's a magnitude word or specific chars
            (?P<magnitude_word>{_magnitude_word_pattern}) # Word magnitude (MILLION, K, THOUSAND etc.)
            |                                   # OR
            (?P<magnitude_char>[KkMmBbTt])(?![A-Za-z]) # Single char magnitude (K,M,B,T) not followed by another letter
                                                # This prevents 'M' in 'Male' or 'T' in 'Test'
        )?
        \s*                                     # Optional trailing whitespace
        $                                       # End of string
        """,
        re.VERBOSE
        | re.IGNORECASE,  # Verbose for comments, Ignorecase for magnitudes/chars
    )

    def _convert_currency(text: Optional[str]) -> Optional[float]:
        if not isinstance(text, str):
            return None

        match = _strict_currency_re.fullmatch(text.strip())
        if not match:
            return None

        try:
            g = match.groupdict()
            number_str = g["number_val"].replace(",", "")
            number_val = Decimal(number_str)

            if g["sign"] == "-":
                number_val = -number_val

            exponent = 0
            magnitude_word = g["magnitude_word"]
            magnitude_char = g["magnitude_char"]

            if magnitude_word:
                exponent = MAGNITUDE.get(magnitude_word.upper(), 0)
            elif magnitude_char:
                # Handle single character magnitudes (ensure they are uppercase for dict lookup)
                char_upper = magnitude_char.upper()
                if char_upper == "T":
                    exponent = 12
                elif char_upper == "B":
                    exponent = 9
                elif char_upper == "M":
                    exponent = 6
                elif char_upper == "K":
                    exponent = 3

            amount = number_val * (Decimal(10) ** exponent)
            return float(amount)
        except Exception:
            return None

    schema = df.collect_schema()
    suspicious_cols = [
        col for col, dtype in schema.items() if dtype == pl.String()
    ]
    if not suspicious_cols:
        return df

    conversion_map: Dict[str, pl.Float64] = {}

    for col in suspicious_cols:
        samples_series = (
            df.select(pl.col(col).cast(pl.String()))
            .head(sample_size)
            .drop_nulls()
            .collect()
            .get_column(col)
        )
        samples = [
            s
            for s in samples_series.to_list()
            if s is not None and isinstance(s, str)
        ]
        success_count = 0

        for sample in samples:
            if not re.search(r"\d", sample):
                continue
            if _convert_currency(sample) is not None:
                success_count += 1

        if success_count / sample_size > 0.8:
            conversion_map[col] = pl.Float64()

    if not conversion_map:
        return df

    return df.with_columns(
        [
            pl.col(col)
            .map_elements(
                lambda x: _convert_currency(x),
                return_dtype=pl.Float64(),
                skip_nulls=True,
            )
            .alias(col)
            for col in conversion_map.keys()
        ]
    )
