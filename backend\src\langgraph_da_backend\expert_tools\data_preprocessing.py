import json
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional, Tuple

from langgraph_da_backend.utils.tool.tool_card import register_tool, tool_card


@register_tool
@tool_card(
    keywords=[
        "data",
        "split",
        "train",
        "test",
        "machine learning",
        "modeling",
        "disabled",
    ],
    description="Splits a dataset into training and testing sets for machine learning and saves them to files.",
)
def split_data_for_modeling(
    input_file_path: Annotated[str, "Path to the input CSV or Parquet file."],
    feature_cols: Annotated[
        List[str], "List of column names to be used as features."
    ],
    target_col: Annotated[str, "The name of the target variable column."],
    output_dir: Annotated[
        str, "Directory where the output train/test split files will be saved."
    ],
    test_size: Annotated[
        float, "The proportion of the dataset to allocate to the test split."
    ] = 0.2,
    random_state: Annotated[
        Optional[int],
        "Seed for the random number generator for reproducibility.",
    ] = 42,
) -> Dict[str, Any]:
    """
    Splits a dataset into training and testing sets (X_train, X_test, y_train, y_test).

    The resulting splits are saved as Parquet files for efficiency and the paths to these files are returned.
    This is the first step in a typical machine learning pipeline.
    """
    try:
        import pandas as pd  # type: ignore
        from sklearn.model_selection import train_test_split  # type: ignore
        from sklearn.preprocessing import LabelEncoder  # type: ignore

        # --- Load Data ---
        p_input = Path(input_file_path)
        if not p_input.exists():
            return {"error": f"File not found: {input_file_path}"}

        if p_input.suffix == ".csv":
            df = pd.read_csv(p_input)
        elif p_input.suffix == ".parquet":
            df = pd.read_parquet(p_input)
        else:
            return {
                "error": f"Unsupported file type: {p_input.suffix}. Use .csv or .parquet."
            }

        # --- Validate Columns ---
        X = df[feature_cols]
        y = df[target_col]

        # --- Handle Categorical Target ---
        if y.dtype == "object" or y.dtype.name == "category":
            le = LabelEncoder()
            y = pd.Series(le.fit_transform(y), index=y.index, name=y.name)
            # TODO: Save the label encoder for inverse transform later if needed.

        # --- Split Data ---
        X_train, X_test, y_train, y_test = train_test_split(
            X,
            y,
            test_size=test_size,
            random_state=random_state,
            stratify=y if y.nunique() > 1 else None,
        )

        # --- Save Splits to Files ---
        p_output = Path(output_dir)
        p_output.mkdir(parents=True, exist_ok=True)

        output_paths = {
            "X_train_path": str(p_output / "X_train.parquet"),
            "X_test_path": str(p_output / "X_test.parquet"),
            "y_train_path": str(p_output / "y_train.parquet"),
            "y_test_path": str(p_output / "y_test.parquet"),
        }

        X_train.to_parquet(output_paths["X_train_path"])
        X_test.to_parquet(output_paths["X_test_path"])
        y_train.to_frame().to_parquet(output_paths["y_train_path"])
        y_test.to_frame().to_parquet(output_paths["y_test_path"])

        return {
            "status": "success",
            "output_paths": output_paths,
            "summary": {
                "train_shape": X_train.shape,
                "test_shape": X_test.shape,
                "target_distribution_train": y_train.value_counts().to_dict(),
                "target_distribution_test": y_test.value_counts().to_dict(),
            },
        }
    except KeyError as e:
        return {
            "error": f"Column not found: {e}. Ensure feature and target columns exist in the file."
        }
    except Exception as e:
        return {"error": f"Failed to split data: {str(e)}"}


@register_tool
@tool_card(
    keywords=[
        "preprocess",
        "features",
        "one-hot",
        "encoding",
        "machine-learning",
        "disabled",
    ],
    description="Applies preprocessing steps like one-hot encoding to training and testing feature sets.",
)
def preprocess_features_for_modeling(
    X_train_path: Annotated[
        str, "Path to the training features file (X_train.parquet)."
    ],
    X_test_path: Annotated[
        str, "Path to the testing features file (X_test.parquet)."
    ],
    feature_definitions: Annotated[
        Dict[str, str],
        "Dictionary defining feature types, e.g., {'col1': 'categorical', 'col2': 'numeric'}.",
    ],
    output_dir: Annotated[
        str, "Directory to save the processed feature files."
    ],
) -> Dict[str, Any]:
    """
    Applies one-hot encoding to categorical features to prepare data for model training.

    This function ensures that the training and testing sets are processed consistently.
    The processed data, along with the final feature names, are saved to files.
    """
    try:
        import pandas as pd  # type: ignore

        # --- Load Data ---
        X_train = pd.read_parquet(X_train_path)
        X_test = pd.read_parquet(X_test_path)

        X_train_processed = X_train.copy()
        X_test_processed = X_test.copy()

        categorical_cols = [
            col
            for col, f_type in feature_definitions.items()
            if f_type == "categorical"
        ]

        # --- One-Hot Encode ---
        if categorical_cols:
            X_train_processed = pd.get_dummies(
                X_train_processed,
                columns=categorical_cols,
                dummy_na=False,
                drop_first=False,
            )
            X_test_processed = pd.get_dummies(
                X_test_processed,
                columns=categorical_cols,
                dummy_na=False,
                drop_first=False,
            )

            # --- Align Columns ---
            train_cols = X_train_processed.columns
            test_cols = X_test_processed.columns

            missing_in_test = set(train_cols) - set(test_cols)
            for c in missing_in_test:
                X_test_processed[c] = 0

            extra_in_test = set(test_cols) - set(train_cols)
            if extra_in_test:
                X_test_processed = X_test_processed.drop(
                    columns=list(extra_in_test)
                )

            X_test_processed = X_test_processed[train_cols]
            processed_feature_names = list(train_cols)
        else:
            processed_feature_names = list(X_train.columns)

        # --- Save Processed Data ---
        p_output = Path(output_dir)
        p_output.mkdir(parents=True, exist_ok=True)

        output_paths = {
            "X_train_processed_path": str(
                p_output / "X_train_processed.parquet"
            ),
            "X_test_processed_path": str(p_output / "X_test_processed.parquet"),
            "feature_names_path": str(p_output / "feature_names.json"),
        }

        X_train_processed.to_parquet(output_paths["X_train_processed_path"])
        X_test_processed.to_parquet(output_paths["X_test_processed_path"])
        with open(output_paths["feature_names_path"], "w") as f:
            json.dump(processed_feature_names, f)

        return {
            "status": "success",
            "output_paths": output_paths,
            "summary": {
                "original_feature_count": len(X_train.columns),
                "processed_feature_count": len(processed_feature_names),
                "categorical_features_encoded": categorical_cols,
            },
        }
    except Exception as e:
        return {"error": f"Failed to preprocess features: {str(e)}"}


@register_tool
@tool_card(
    keywords=[
        "normalize",
        "scale",
        "minmax",
        "zscore",
        "robust",
        "numeric",
        "preprocess",
    ],
)
def normalize_data(
    input_file_path: Annotated[
        str, "Path to the input data file (CSV or Parquet)."
    ],
    output_dir: Annotated[str, "Directory to save the normalized data file."],
    columns: Annotated[List[str], "List of numeric columns to normalize."],
    method: Annotated[
        str, "Normalization method: 'minmax', 'zscore', or 'robust'."
    ] = "minmax",
) -> Dict[str, Any]:
    """
    Applies numerical scaling to specified columns to bring them into a unified range. Necessary for some machine learning and clustering algorithms.

    - Supports Min-Max, Z-score, and Robust scaling.
    """
    try:
        import polars as pl  # type: ignore

        p_input = Path(input_file_path)
        if not p_input.exists():
            return {"error": f"File not found: {input_file_path}"}

        df = (
            pl.read_parquet(p_input)
            if p_input.suffix == ".parquet"
            else pl.read_csv(p_input)
        )

        report: Dict[str, Any] = {"actions": [], "initial_shape": df.shape}

        for col in columns:
            if not df[col].dtype.is_numeric():
                report["actions"].append(
                    f"Column '{col}': Skipped, not a numeric type."
                )
                continue

            if method == "minmax":
                min_val, max_val = df[col].min(), df[col].max()
                if (
                    min_val is not None
                    and max_val is not None
                    and min_val < max_val
                ):
                    df = df.with_columns(
                        ((pl.col(col) - min_val) / (max_val - min_val)).alias(
                            col
                        )
                    )
                    report["actions"].append(
                        f"Column '{col}': Applied Min-Max scaling."
                    )
            elif method == "zscore":
                mean, std = df[col].mean(), df[col].std()
                if std is not None and std > 0 and mean is not None:
                    df = df.with_columns(
                        ((pl.col(col) - mean) / std).alias(col)
                    )
                    report["actions"].append(
                        f"Column '{col}': Applied Z-score scaling."
                    )
            elif method == "robust":
                q1, q3 = df[col].quantile(0.25), df[col].quantile(0.75)
                median = df[col].median()
                if q1 is None or q3 is None or median is None:
                    report["actions"].append(
                        f"Column '{col}': Skipped, could not compute quantiles (likely all nulls)."
                    )
                    continue
                iqr = q3 - q1
                if iqr is not None and iqr > 0:
                    df = df.with_columns(
                        ((pl.col(col) - median) / iqr).alias(col)
                    )
                    report["actions"].append(
                        f"Column '{col}': Applied Robust scaling (IQR)."
                    )
            else:
                return {"error": f"Invalid normalization method: {method}"}

        p_output = Path(output_dir)
        p_output.mkdir(parents=True, exist_ok=True)
        output_file_path = str(p_output / f"{p_input.stem}_normalized.parquet")
        df.write_parquet(output_file_path)

        report["final_shape"] = df.shape

        return {
            "status": "success",
            "output_file_path": output_file_path,
            "summary": report,
        }
    except Exception as e:
        return {"error": f"Failed during data normalization: {str(e)}"}
