DA_SANITY_CHECK_TEMPLATE = """
Check whether the user's query indicates a data analysis task or can be solved by data analysis.

User query:
{query}

Output instructions/format:
{format_instructions}
""".strip()

DA_REWRITE_TEMPLATE = """
Rewrite the user's query into a formal data analysis task. 

The relevant data schema is as follows:
{data_schema}

The original user query is:
'{query}'

The rewritten query should clarify the following aspects:
- What kind of this analysis should be performed? 
- What specific metrics or key indicators should be analyzed? Does these metrics exist in the original data or need to be calculated?
- What models or algorithms could be applied to provide insights? 

Output instructions/format:
{format_instructions}
"""

ABSTRACT_PLAN_TEMPLATE = """
Decompose the user's request into actionable steps. The steps would be executed in a sequential order: `data preparation`, `data analysis`, and optional `visualization`.

The original user request is:
'{query}'

A descriptive analysis has been performed on the mentioned data, as follows:
{data_description}

The decomposed steps should stick to the following guidelines:
1. Provide only what-to-do instructions. DO NOT include any explanation or ambiguous conditional statements like examples. If a step can be implemented in multiple ways, choose the most informative, most fine-grained one. Give clear, deterministic instructions.
2. All transformations should be finalized within the data preparation step. The data analysis step only performs computation on the prepared dataset.
3. Each of the data operation should specify the columns to work on.
""".strip()

ABSTRACT_PLAN_PARSE_TEMPLATE = """
{query}

{format_instructions}
""".strip()

DE_PIPELINE_TEMPLATE = """
Determine the operation details for preparing the data for downstream analysis.

The data schema (columns and data types) are as follows:
{data_schema}

The hints for curating the dataset: 
{curation_hint}

Constraints:
1. DO NOT refer to non-existent columns. Predefined tools only operate on existed columns.
2. Give clear what-to-do instructions only. DO NOT include any explanation or ambiguous conditional statements like examples.

Output instructions/format:  
{format_instructions}
""".strip()

DE_CODE_CURATION_TEMPLATE = """
Write a data processing python script to be executed in a sandbox environment, which is equivalent to pass the code to the `exec()` function.

The data schema (columns and data types) are as follows:
{data_schema}

The hints for processing the dataset:
{curation_hint}

Constraints:
1. Provide only the raw code that can be directly executed. DO NOT include any non-codeblock descriptions or explanations.
2. Except for built-in Python modules, only the following modules are allowed: `numpy`(`np`), `pandas`(`pd`), `plotly`, `polars`(`pl`), `scipy`, `sklearn`.
3. The data object which you need to operate on is provided as an in-memory python variable named `df`, whose type is `pandas.DataFrame`. This is the only available variable at the start.
4. Assign the processed data object into two new variables, both of which should be `pandas.DataFrame` type.
    - If the processed data contains a subset of the original data columns, e.g., row-wise filtering, column-wise transformation or new column addition, assign the result data object to `df_refined` variable.
    - If the processed data is an aggregation or other forms of tranformation and does not contain any original data columns, assign the result data object to `df_new` variable.
""".strip()

DA_DESCRIPTIVE_SUFFICIENCY_TEMPLATE = """
Based on the user's original question and the descriptive analysis results, determine if the descriptive analysis is sufficient to answer the user's question.

User's original question:
{user_question}

Descriptive analysis results:
{descriptive_results}

Consider whether the descriptive statistics, correlations, and data summaries provide enough information to fully answer the user's question, or if additional analysis (statistical tests, modeling, time series analysis, etc.) is needed.

Output instructions/format:
{format_instructions}
""".strip()

DA_FUNCTION_EXECUTION_TEMPLATE = """
Based on the data analysis plan and the available functions, determine the appropriate function to execute and its parameters.

Data analysis plan:
{data_analysis_plan}

Available functions (top 3 most relevant):
{available_functions}

Current data schemas:
- refined_df: {refined_df_schema}
- renewed_df: {renewed_df_schema}

Choose the most appropriate function and specify:
1. The function name
2. Which dataframe to use (refined_df or renewed_df)
3. The exact parameters to pass to the function

Output instructions/format:
{format_instructions}
""".strip()

DA_FINAL_ANSWER_TEMPLATE = """
Generate a comprehensive answer to the user's question based on all available analysis results.

User's original question:
{user_question}

Available analysis results:
{analysis_results}

Provide a detailed answer that:
1. Directly addresses the user's question
2. Includes specific numeric details and insights
3. Explains the methodology used
4. Highlights key findings

Output instructions/format:
{format_instructions}
""".strip()

DA_CODE_GENERATION_TEMPLATE = """
Generate Python code to perform the required data analysis based on the data analysis plan.

Data analysis plan:
{data_analysis_plan}

Available dataframes:
- refined_df: {refined_df_schema}
- renewed_df: {renewed_df_schema}

Constraints:
1. Provide only the raw code that can be directly executed. DO NOT include any non-codeblock descriptions or explanations.
2. Except for built-in Python modules, only the following modules are allowed: `numpy`(`np`), `pandas`(`pd`), `plotly`, `polars`(`pl`), `scipy`, `sklearn`.
3. The dataframes `refined_df` and `renewed_df` are available as pandas DataFrames.
4. Store your analysis results in a variable called `analysis_result` as a dictionary containing your findings.
""".strip()
