import json
import os
from pprint import pprint

from langchain.chat_models import init_chat_model
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langgraph_da_backend.basic_tools.tool_schema import DataProcessingPipeline
from langgraph_da_backend.exec_engine.prompt_template import TEMPLATE
from langgraph_da_backend.exec_engine.utils import (
    extract_python_code,
    run_sandboxed_code,
)

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

llm_pipeline = init_chat_model(
    "gpt-4.1",
    model_provider="openai",
    base_url="https://api.apiyi.com/v1",
    api_key=OPENAI_API_KEY,
    streaming=False,
    temperature=0,
    use_responses_api=False,  # ! I notice Response API has very high delay, not sure if it's due to the API provider. Anyway, we don't need it for now as we use general chat.
)

llm_code = init_chat_model(
    "gemini-2.5-pro",
    model_provider="openai",
    base_url="https://api.apiyi.com/v1",
    api_key=OPENAI_API_KEY,
    streaming=False,
)

# TODO: tune the prompt & better integration with JsonOutputParser
pipeline_prompt = f"""
## Task
You are a data engineering expert, and you are preprocessing a dataset for further analysis.
You must generate a JSON object that strictly adheres to the following JSON schema. The output MUST be a valid JSON object and nothing else. 
DO NOT mention non-existed data columns.

## Data Schema
ID: str
ServiceType: str
ServiceStart: datetime64
ServiceEnd: datetime64

## User Request
'what is the trend of the service duration changing over time?'

## Schema
{json.dumps(DataProcessingPipeline.model_json_schema())}
""".strip()

parser = JsonOutputParser(pydantic_object=DataProcessingPipeline)

chain = llm_pipeline | parser

# ! the user should be able to adjust the option here.
tool_chain_paras = chain.invoke(pipeline_prompt)
print(type(tool_chain_paras))
pprint(tool_chain_paras)

extra_hint = tool_chain_paras.pop("further_curation")

code_prompt = f"""
## Current Dataset Schema

```
ID: str
ServiceType: str
ServiceStart: datetime64
ServiceEnd: datetime64
```

## User Request

'what is the trend of the service duration changing over time?'

## Applied Operations (no need to execute again)

```
{dict([(k,v) for k,v in tool_chain_paras.items() if v is not None and not k == "further_curation"])}
```

## Next Operation Hint

{extra_hint}

## Constraint

Only perform the required data processing operations on the dataset, do not perform any other analysis or visualization.
"""

code_prompt = TEMPLATE + "\n" + code_prompt

code_chain_result = llm_code.invoke(code_prompt)
print(code_chain_result.content)
print(extract_python_code(code_chain_result.content))  # type: ignore
