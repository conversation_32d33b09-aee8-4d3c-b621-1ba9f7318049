import json
from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional

from ..utils.tool.tool_card import register_tool, tool_card

# It's assumed that pandas, scikit-learn, and joblib are installed.
# These imports will be resolved at runtime.


@register_tool
@tool_card(
    keywords=[
        "predict",
        "model",
        "inference",
        "machine learning",
        "score",
        "disabled",
    ],
)
def run_prediction(
    model_path: Annotated[
        str, "Path to the trained model artifact (.joblib file)."
    ],
    X_test_path: Annotated[
        str,
        "Path to the processed test features file (X_test_processed.parquet).",
    ],
    output_dir: Annotated[str, "Directory to save the prediction results."],
) -> Dict[str, Any]:
    """
    Loads a trained model and uses it to generate predictions on a test set.

    For classification tasks, it generates both class predictions and probability scores.
    """
    try:
        import joblib  # type: ignore
        import pandas as pd  # type: ignore

        # --- Load Model and Data ---
        model = joblib.load(model_path)
        X_test = pd.read_parquet(X_test_path)

        # --- Generate Predictions ---
        predictions = model.predict(X_test)

        output_data = {"predictions": predictions.tolist()}
        task_type = "regression"  # Assume regression by default

        # --- Generate Probabilities for Classification ---
        if hasattr(model, "predict_proba"):
            task_type = "classification"
            try:
                probabilities = model.predict_proba(X_test)
                # Save probability for the positive class (class 1)
                output_data["probabilities_class_1"] = probabilities[
                    :, 1
                ].tolist()
            except Exception as e:
                output_data["probabilities_error"] = str(e)

        # --- Save Predictions ---
        p_output = Path(output_dir)
        p_output.mkdir(parents=True, exist_ok=True)
        predictions_path = str(p_output / "predictions.json")
        with open(predictions_path, "w") as f:
            json.dump(output_data, f)

        return {
            "status": "success",
            "predictions_path": predictions_path,
            "task_type_inferred": task_type,
            "summary": {
                "model_path": model_path,
                "test_samples": len(X_test),
                "has_probabilities": "probabilities_class_1" in output_data,
            },
        }
    except Exception as e:
        return {"error": f"Failed to run prediction: {str(e)}"}


@register_tool
@tool_card(
    keywords=[
        "evaluate",
        "model",
        "metrics",
        "performance",
        "accuracy",
        "mse",
        "disabled",
    ],
)
def evaluate_model_performance(
    task_type: Annotated[
        str, "The type of task: 'regression' or 'classification'."
    ],
    y_test_path: Annotated[
        str, "Path to the test target variable file (y_test.parquet)."
    ],
    predictions_path: Annotated[
        str, "Path to the predictions file (predictions.json)."
    ],
) -> Dict[str, Any]:
    """
    Calculates performance metrics for a regression or classification model.

    For regression: Calculates Mean Squared Error and R-squared.
    For classification: Calculates Accuracy, Precision, Recall, F1-score, ROC AUC, and the Confusion Matrix.
    """
    try:
        import numpy as np  # type: ignore
        import pandas as pd  # type: ignore
        from sklearn.metrics import (  # type: ignore
            accuracy_score,
            confusion_matrix,
            mean_squared_error,
            precision_recall_fscore_support,
            r2_score,
            roc_auc_score,
        )

        # --- Load Data ---
        y_test = pd.read_parquet(y_test_path).iloc[:, 0]
        with open(predictions_path, "r") as f:
            pred_data = json.load(f)

        y_pred = np.array(pred_data["predictions"])

        # --- Evaluate ---
        if task_type == "regression":
            mse = mean_squared_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            metrics = {"mean_squared_error": mse, "r2_score": r2}
        elif task_type == "classification":
            accuracy = accuracy_score(y_test, y_pred)
            precision, recall, f1, _ = precision_recall_fscore_support(
                y_test, y_pred, average="binary"
            )
            cm = confusion_matrix(y_test, y_pred)

            metrics = {
                "accuracy": accuracy,
                "precision": precision,
                "recall": recall,
                "f1_score": f1,
                "confusion_matrix": cm.tolist(),
            }

            if "probabilities_class_1" in pred_data:
                y_prob = np.array(pred_data["probabilities_class_1"])
                try:
                    auc = roc_auc_score(y_test, y_prob)
                    metrics["roc_auc_score"] = auc
                except Exception as auc_err:
                    metrics["roc_auc_score"] = f"Calculation Error: {auc_err}"
        else:
            return {
                "error": "Invalid task_type. Must be 'regression' or 'classification'."
            }

        return {"status": "success", "evaluation_metrics": metrics}
    except Exception as e:
        return {"error": f"Failed to evaluate model: {str(e)}"}


@register_tool
@tool_card(
    keywords=[
        "interpret",
        "model",
        "explain",
        "features",
        "coefficients",
        "importance",
        "disabled",
    ],
    description="Interprets a trained model to understand feature influence.",
)
def interpret_trained_model(
    model_path: Annotated[
        str, "Path to the trained model artifact (.joblib file)."
    ],
    feature_names_path: Annotated[
        str, "Path to the JSON file containing the list of feature names."
    ],
) -> Dict[str, Any]:
    """
    Provides model interpretation by extracting feature coefficients or importances.

    Supports linear models, logistic regression, and tree-based models.
    """
    try:
        import joblib  # type: ignore
        import numpy as np

        # --- Load Model and Features ---
        model = joblib.load(model_path)
        with open(feature_names_path, "r") as f:
            feature_names = json.load(f)

        interpretation: Dict[str, Any] = {}

        # --- Interpret ---
        if hasattr(model, "coef_"):  # Linear models, Logistic Regression
            interpretation_type = "coefficients"
            if model.coef_.ndim > 1:  # Handle multi-class case
                coefs = model.coef_[0]
            else:
                coefs = model.coef_.flatten()

            interpretation_data: Any = dict(zip(feature_names, coefs))
            if hasattr(model, "intercept_"):
                interpretation["intercept"] = (
                    model.intercept_[0]
                    if isinstance(model.intercept_, (list, np.ndarray))
                    else model.intercept_
                )

        elif hasattr(model, "feature_importances_"):  # Tree-based models
            interpretation_type = "feature_importances"
            importances = model.feature_importances_
            interpretation_data = dict(zip(feature_names, importances))
        else:
            interpretation_type = "unsupported"
            interpretation_data = f"Standard interpretation not available for {type(model).__name__}."

        interpretation[interpretation_type] = interpretation_data

        return {"status": "success", "interpretation": interpretation}
    except Exception as e:
        return {"error": f"Failed to interpret model: {str(e)}"}


@register_tool
@tool_card(
    keywords=[
        "report",
        "summary",
        "prediction",
        "results",
        "format",
        "markdown",
        "disabled",
    ],
    description="Formats prediction, evaluation, and interpretation results into a comprehensive Markdown report.",
)
def format_prediction_report(
    model_summary: Annotated[
        Dict[str, Any], "The summary dictionary returned by the training tool."
    ],
    evaluation_metrics: Annotated[
        Dict[str, Any],
        "The evaluation_metrics dictionary returned by the evaluation tool.",
    ],
    interpretation: Annotated[
        Dict[str, Any],
        "The interpretation dictionary returned by the interpretation tool.",
    ],
) -> str:
    """
    Creates a detailed Markdown report summarizing the entire prediction workflow,
    including model details, performance metrics, and feature insights.
    """
    report = [
        f"## Prediction Report: {model_summary.get('model_name', 'N/A')}\n"
    ]

    # --- Model Details ---
    report.append("### 1. Model & Task Details\n")
    report.append(
        f"- **Task Type:** {model_summary.get('task_type', 'N/A').capitalize()}"
    )
    report.append(f"- **Model:** `{model_summary.get('model_name', 'N/A')}`")
    params = model_summary.get("hyperparameters_used", {})
    if params:
        param_str = ", ".join([f"`{k}`={v}" for k, v in params.items()])
        report.append(f"- **Hyperparameters:** {param_str}")
    report.append("\n")

    # --- Evaluation Metrics ---
    report.append("### 2. Performance Evaluation\n")
    if model_summary.get("task_type") == "regression":
        report.append(
            f"- **Mean Squared Error:** {evaluation_metrics.get('mean_squared_error', 'N/A'):.4f}"
        )
        report.append(
            f"- **R-squared (R²):** {evaluation_metrics.get('r2_score', 'N/A'):.4f}"
        )
    elif model_summary.get("task_type") == "classification":
        report.append(
            f"- **Accuracy:** {evaluation_metrics.get('accuracy', 'N/A'):.4f}"
        )
        report.append(
            f"- **Precision:** {evaluation_metrics.get('precision', 'N/A'):.4f}"
        )
        report.append(
            f"- **Recall:** {evaluation_metrics.get('recall', 'N/A'):.4f}"
        )
        report.append(
            f"- **F1-score:** {evaluation_metrics.get('f1_score', 'N/A'):.4f}"
        )
        report.append(
            f"- **ROC AUC Score:** {evaluation_metrics.get('roc_auc_score', 'N/A'):.4f}"
        )
        if "confusion_matrix" in evaluation_metrics:
            cm = evaluation_metrics["confusion_matrix"]
            report.append("- **Confusion Matrix:** `[[TN, FP], [FN, TP]]`")
            report.append(
                f"  ```\n  [[{cm[0][0]}, {cm[0][1]}],\n   [{cm[1][0]}, {cm[1][1]}]]\n  ```"
            )
    report.append("\n")

    # --- Interpretation ---
    report.append("### 3. Model Interpretation\n")
    if "coefficients" in interpretation:
        report.append("#### Top 10 Feature Coefficients (by absolute value)\n")
        data = interpretation["coefficients"]
        sorted_data = sorted(
            data.items(), key=lambda item: abs(item[1]), reverse=True
        )
        report.append("| Feature | Coefficient |")
        report.append("|---------|-------------|")
        for feature, val in sorted_data[:10]:
            report.append(f"| `{feature}` | {val:.4f} |")
        report.append(
            f"\n- **Intercept:** {interpretation.get('intercept', 'N/A'):.4f}"
        )

    elif "feature_importances" in interpretation:
        report.append("#### Top 10 Feature Importances\n")
        data = interpretation["feature_importances"]
        sorted_data = sorted(
            data.items(), key=lambda item: item[1], reverse=True
        )
        report.append("| Feature | Importance |")
        report.append("|---------|------------|")
        for feature, val in sorted_data[:10]:
            report.append(f"| `{feature}` | {val:.4f} |")

    else:
        report.append(
            "- No standard interpretation data available for this model type."
        )

    return "\n".join(report)
