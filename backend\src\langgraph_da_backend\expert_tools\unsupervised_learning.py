from pathlib import Path
from typing import Annotated, Any, Dict, List, Optional

import joblib  # type: ignore
from langgraph_da_backend.utils.tool.tool_card import register_tool, tool_card


def _read_data_to_pandas(input_file_path: str):
    """Reads a CSV or Parquet file into a pandas DataFrame."""
    import pandas as pd  # type: ignore

    p_input = Path(input_file_path)
    if not p_input.exists():
        raise FileNotFoundError(f"Input file not found: {input_file_path}")

    if p_input.suffix == ".parquet":
        return pd.read_parquet(p_input)
    else:
        return pd.read_csv(p_input)


def _save_artifact(artifact: Any, output_dir: str, filename: str) -> str:
    """Saves a Python object (like a model or scaler) to a file using joblib."""
    p_output = Path(output_dir)
    p_output.mkdir(parents=True, exist_ok=True)
    output_path = p_output / filename
    joblib.dump(artifact, output_path)
    return str(output_path)


@register_tool
@tool_card(
    keywords=["preprocess", "scale", "impute", "unsupervised", "disabled"],
)
def preprocess_for_unsupervised(
    input_file_path: Annotated[
        str, "Path to the input data file (CSV or Parquet)."
    ],
    output_dir: Annotated[
        str, "Directory to save the processed data and scaler object."
    ],
    feature_cols: Annotated[
        List[str], "List of columns to be used as features."
    ],
    missing_strategy: Annotated[
        str,
        "Strategy for imputing missing values: 'mean', 'median', or 'most_frequent'.",
    ] = "mean",
    scaling_strategy: Annotated[
        str, "Scaling method: 'standard' (Z-score) or 'minmax'."
    ] = "standard",
) -> Dict[str, Any]:
    """
    Handles feature preprocessing for unsupervised learning tasks. It imputes missing data,
    scales numerical features, and saves the processed data and the scaler object.
    """
    try:
        import pandas as pd  # type: ignore
        from sklearn.impute import SimpleImputer  # type: ignore
        from sklearn.preprocessing import MinMaxScaler, StandardScaler  # type: ignore

        df = _read_data_to_pandas(input_file_path)
        X = df[feature_cols].copy()

        # Handle Missing Values
        if X.isnull().sum().any():
            imputer = SimpleImputer(strategy=missing_strategy)
            X = pd.DataFrame(
                imputer.fit_transform(X), columns=X.columns, index=X.index
            )

        # Feature Scaling
        if scaling_strategy == "standard":
            scaler = StandardScaler()
        elif scaling_strategy == "minmax":
            scaler = MinMaxScaler()
        else:
            return {
                "error": f"Unsupported scaling strategy: {scaling_strategy}"
            }

        X_scaled = scaler.fit_transform(X)
        processed_df = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)

        # Save artifacts
        p_input = Path(input_file_path)
        processed_data_path = str(
            Path(output_dir) / f"{p_input.stem}_processed.parquet"
        )
        processed_df.to_parquet(processed_data_path)
        scaler_path = _save_artifact(
            scaler, output_dir, f"{p_input.stem}_scaler.joblib"
        )

        return {
            "status": "success",
            "processed_data_path": processed_data_path,
            "scaler_path": scaler_path,
            "summary": f"Data preprocessed with '{missing_strategy}' imputation and '{scaling_strategy}' scaling.",
        }
    except Exception as e:
        return {"error": f"Failed during preprocessing: {str(e)}"}


@register_tool
@tool_card(
    keywords=["cluster", "kmeans", "dbscan", "agglomerative"],
)
def run_clustering(
    input_file_path: Annotated[str, "Path to the preprocessed data file."],
    original_data_path: Annotated[
        str, "Path to the original, unprocessed data file."
    ],
    output_dir: Annotated[
        str, "Directory to save the labeled data and model object."
    ],
    model_type: Annotated[
        str, "Clustering algorithm: 'kmeans', 'dbscan', or 'agglomerative'."
    ],
    hyperparameters: Annotated[
        Optional[Dict[str, Any]], "Hyperparameters for the clustering model."
    ] = None,
) -> Dict[str, Any]:
    """
    Applies a clustering algorithm (KMeans, DBSCAN, etc.) to the data.
    """
    try:
        import numpy as np  # type: ignore
        from sklearn.cluster import (  # type: ignore
            DBSCAN,
            AgglomerativeClustering,
            KMeans,
        )

        CLUSTERING_MODELS = {
            "kmeans": KMeans,
            "dbscan": DBSCAN,
            "agglomerative": AgglomerativeClustering,
        }
        if model_type not in CLUSTERING_MODELS:
            return {"error": f"Unsupported clustering model: {model_type}"}

        processed_df = _read_data_to_pandas(input_file_path)
        original_df = _read_data_to_pandas(original_data_path)

        ModelClass = CLUSTERING_MODELS[model_type]
        model = ModelClass(**(hyperparameters or {}))

        model.fit(processed_df)
        labels = model.labels_

        # Save labeled data
        labeled_df = original_df.copy()
        labeled_df["cluster_label"] = labels
        p_input = Path(input_file_path)
        labeled_data_path = str(
            Path(output_dir) / f"{p_input.stem}_clustered.parquet"
        )
        labeled_df.to_parquet(labeled_data_path)

        # Save model
        model_path = _save_artifact(
            model, output_dir, f"{model_type}_model.joblib"
        )

        return {
            "status": "success",
            "labeled_data_path": labeled_data_path,
            "model_path": model_path,
            "summary": f"Clustering complete. Found {len(np.unique(labels))} clusters using {model_type}.",
        }
    except Exception as e:
        return {"error": f"Failed during clustering: {str(e)}"}


@register_tool
@tool_card(
    keywords=[
        "evaluate",
        "clustering",
        "silhouette",
        "davies-bouldin",
        "disabled",
    ],
    description="Evaluates clustering performance using standard metrics like Silhouette Score.",
)
def evaluate_clustering_performance(
    processed_data_path: Annotated[
        str, "Path to the preprocessed data file used for clustering."
    ],
    labeled_data_path: Annotated[
        str, "Path to the data file with cluster labels."
    ],
) -> Dict[str, Any]:
    """
    Calculates clustering evaluation metrics such as the Silhouette Score and Davies-Bouldin Score.
    Requires the preprocessed data and the data with assigned cluster labels.
    """
    try:
        import numpy as np  # type: ignore
        from sklearn.metrics import (  # type: ignore
            davies_bouldin_score,
            silhouette_score,
        )

        processed_df = _read_data_to_pandas(processed_data_path)
        labeled_df = _read_data_to_pandas(labeled_data_path)
        labels = labeled_df["cluster_label"]

        n_clusters = len(np.unique(labels[labels != -1]))
        if n_clusters <= 1:
            return {
                "warning": f"Only {n_clusters} cluster(s) found. Cannot calculate metrics."
            }

        metrics = {
            "silhouette_score": silhouette_score(processed_df, labels),
            "davies_bouldin_score": davies_bouldin_score(processed_df, labels),
            "num_clusters": n_clusters,
        }
        return {"status": "success", "metrics": metrics}
    except Exception as e:
        return {"error": f"Failed during clustering evaluation: {str(e)}"}


@register_tool
@tool_card(
    keywords=["dimensionality reduction", "pca", "tsne", "umap"],
)
def run_dimensionality_reduction(
    input_file_path: Annotated[str, "Path to the preprocessed data file."],
    output_dir: Annotated[
        str, "Directory to save the reduced data and model object."
    ],
    model_type: Annotated[
        str, "Reduction algorithm: 'pca', 'tsne', or 'umap'."
    ],
    hyperparameters: Annotated[
        Optional[Dict[str, Any]],
        "Hyperparameters for the reduction model (e.g., n_components).",
    ] = None,
) -> Dict[str, Any]:
    """
    Applies a dimensionality reduction technique to the data, saves the transformed
    low-dimensional data, and saves the fitted model.
    """
    try:
        import pandas as pd  # type: ignore
        from sklearn.decomposition import PCA  # type: ignore
        from sklearn.manifold import TSNE  # type: ignore

        try:
            from umap import UMAP  # type: ignore

            UMAP_AVAILABLE = True
        except ImportError:
            UMAP_AVAILABLE = False

        MODELS = {"pca": PCA, "tsne": TSNE}
        if UMAP_AVAILABLE:
            MODELS["umap"] = UMAP

        if model_type not in MODELS:
            return {"error": f"Unsupported reduction model: {model_type}"}

        processed_df = _read_data_to_pandas(input_file_path)
        params = hyperparameters or {}
        if "n_components" not in params:
            params["n_components"] = 2

        ModelClass = MODELS[model_type]
        model = ModelClass(**params)

        X_reduced = model.fit_transform(processed_df)

        comp_names = [
            f"{model_type}_comp_{i+1}" for i in range(X_reduced.shape[1])
        ]
        reduced_df = pd.DataFrame(
            X_reduced, columns=comp_names, index=processed_df.index
        )

        p_input = Path(input_file_path)
        reduced_data_path = str(
            Path(output_dir) / f"{p_input.stem}_reduced_{model_type}.parquet"
        )
        reduced_df.to_parquet(reduced_data_path)

        model_path = _save_artifact(
            model, output_dir, f"{model_type}_reduction_model.joblib"
        )

        return {
            "status": "success",
            "reduced_data_path": reduced_data_path,
            "model_path": model_path,
            "summary": f"Dimensionality reduction to {X_reduced.shape[1]} components complete using {model_type}.",
        }
    except Exception as e:
        return {"error": f"Failed during dimensionality reduction: {str(e)}"}


@register_tool
@tool_card(
    keywords=[
        "anomaly detection",
        "outlier",
        "isolation forest",
        "lof",
        "disabled",
    ],
    description="Detects anomalies in the data using algorithms like Isolation Forest.",
)
def run_anomaly_detection(
    input_file_path: Annotated[str, "Path to the preprocessed data file."],
    original_data_path: Annotated[
        str, "Path to the original, unprocessed data file."
    ],
    output_dir: Annotated[
        str, "Directory to save the data with anomaly labels and the model."
    ],
    model_type: Annotated[
        str,
        "Anomaly detection algorithm: 'isolation_forest', 'local_outlier_factor', or 'one_class_svm'.",
    ],
    hyperparameters: Annotated[
        Optional[Dict[str, Any]],
        "Hyperparameters for the anomaly detection model.",
    ] = None,
) -> Dict[str, Any]:
    """
    Identifies anomalies in the data. It adds an 'anomaly_label' column (-1 for anomalies, 1 for inliers)
    to the original dataset and saves the result, along with the trained model.
    """
    try:
        import numpy as np  # type: ignore
        from sklearn.ensemble import IsolationForest  # type: ignore
        from sklearn.neighbors import LocalOutlierFactor  # type: ignore
        from sklearn.svm import OneClassSVM  # type: ignore

        MODELS = {
            "isolation_forest": IsolationForest,
            "local_outlier_factor": LocalOutlierFactor,
            "one_class_svm": OneClassSVM,
        }
        if model_type not in MODELS:
            return {
                "error": f"Unsupported anomaly detection model: {model_type}"
            }

        processed_df = _read_data_to_pandas(input_file_path)
        original_df = _read_data_to_pandas(original_data_path)

        ModelClass = MODELS[model_type]
        model = ModelClass(**(hyperparameters or {}))

        if model_type == "local_outlier_factor":
            predictions = model.fit_predict(processed_df)
        else:
            model.fit(processed_df)
            predictions = model.predict(processed_df)

        labeled_df = original_df.copy()
        labeled_df["anomaly_label"] = predictions

        p_input = Path(input_file_path)
        labeled_data_path = str(
            Path(output_dir) / f"{p_input.stem}_anomalies.parquet"
        )
        labeled_df.to_parquet(labeled_data_path)

        model_path = _save_artifact(
            model, output_dir, f"{model_type}_anomaly_model.joblib"
        )

        num_anomalies = np.sum(predictions == -1)

        return {
            "status": "success",
            "labeled_data_path": labeled_data_path,
            "model_path": model_path,
            "summary": f"Anomaly detection complete. Found {num_anomalies} anomalies using {model_type}.",
        }
    except Exception as e:
        return {"error": f"Failed during anomaly detection: {str(e)}"}
